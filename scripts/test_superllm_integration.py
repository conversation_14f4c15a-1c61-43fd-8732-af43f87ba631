#!/usr/bin/env python3
"""
Test script for SuperLLM integration.

This script helps users verify that the SuperLLM provider is properly configured
and can communicate with the SuperLLM API server.

Usage:
    python scripts/test_superllm_integration.py [firebase_token]

If no token is provided, the script will use the token from the configuration file.
"""

import sys
import os
import json
import argparse
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from services.llm_clients.superllm_client import SuperLLMClient
from services.llm_clients.llm_factory import llm_client_factory
from config import config


def test_configuration():
    """Test that SuperLLM configuration is properly loaded."""
    print("🔧 Testing SuperLLM configuration...")
    
    try:
        # Check if SuperLLM is configured
        if not hasattr(config.llm, 'superllm') or config.llm.superllm is None:
            print("❌ SuperLLM configuration not found in config file")
            print("   Please add 'superllm' section to your configuration")
            return False
        
        superllm_config = config.llm.superllm
        
        # Check required fields
        required_fields = ['api_endpoint', 'firebase_token', 'default_model', 'default_provider']
        missing_fields = []
        
        for field in required_fields:
            if not hasattr(superllm_config, field) or not getattr(superllm_config, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing required SuperLLM configuration fields: {', '.join(missing_fields)}")
            return False
        
        print("✅ SuperLLM configuration loaded successfully")
        print(f"   API Endpoint: {superllm_config.api_endpoint}")
        print(f"   Default Model: {superllm_config.default_model}")
        print(f"   Default Provider: {superllm_config.default_provider}")
        print(f"   Firebase Token: {'*' * (len(superllm_config.firebase_token) - 10) + superllm_config.firebase_token[-10:] if len(superllm_config.firebase_token) > 10 else '***'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading SuperLLM configuration: {e}")
        return False


def test_client_initialization():
    """Test SuperLLM client initialization."""
    print("\n🚀 Testing SuperLLM client initialization...")
    
    try:
        client = SuperLLMClient()
        print("✅ SuperLLM client initialized successfully")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize SuperLLM client: {e}")
        return None


def test_factory_integration():
    """Test that the factory can create SuperLLM client."""
    print("\n🏭 Testing LLM factory integration...")
    
    try:
        # Temporarily set provider to superllm
        original_provider = config.llm.provider
        config.llm.provider = "superllm"
        
        client = llm_client_factory()
        
        if isinstance(client, SuperLLMClient):
            print("✅ LLM factory creates SuperLLM client correctly")
            return True
        else:
            print(f"❌ LLM factory created {type(client)} instead of SuperLLMClient")
            return False
            
    except Exception as e:
        print(f"❌ Error testing factory integration: {e}")
        return False
    finally:
        # Restore original provider
        config.llm.provider = original_provider


def test_api_call(client, test_prompt="Hello, how are you?"):
    """Test actual API call to SuperLLM."""
    print(f"\n💬 Testing API call with prompt: '{test_prompt}'")
    
    try:
        response = client.call_llm(test_prompt)
        print("✅ API call successful!")
        print(f"   Response: {response[:100]}{'...' if len(response) > 100 else ''}")
        return True
    except Exception as e:
        print(f"❌ API call failed: {e}")
        
        # Provide helpful error messages
        error_str = str(e).lower()
        if "authentication failed" in error_str:
            print("   💡 Tip: Your Firebase token may be expired or invalid.")
            print("      Please re-authenticate via SuperLLM web interface.")
        elif "connection" in error_str:
            print("   💡 Tip: Check that SuperLLM server is running and accessible.")
            print("      Default URL: http://localhost:8000")
        elif "rate limit" in error_str:
            print("   💡 Tip: You've hit the rate limit. Please wait and try again.")
        
        return False


def update_token_in_config(new_token):
    """Update Firebase token in configuration file."""
    print(f"\n🔄 Updating Firebase token in configuration...")
    
    try:
        config_file = os.getenv("SUTRAKNOWLEDGE_CONFIG")
        if not config_file:
            print("❌ SUTRAKNOWLEDGE_CONFIG environment variable not set")
            return False
        
        # Read current config
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        # Update token
        if 'llm' not in config_data:
            config_data['llm'] = {}
        if 'superllm' not in config_data['llm']:
            config_data['llm']['superllm'] = {
                "api_endpoint": "http://localhost:8000",
                "default_model": "gpt-3.5-turbo",
                "default_provider": "openai"
            }
        
        config_data['llm']['superllm']['firebase_token'] = new_token
        
        # Write back to file
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print("✅ Configuration updated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")
        return False


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test SuperLLM integration")
    parser.add_argument("firebase_token", nargs="?", help="Firebase authentication token")
    parser.add_argument("--update-config", action="store_true", help="Update configuration file with new token")
    parser.add_argument("--prompt", default="Hello, how are you?", help="Test prompt to send")
    
    args = parser.parse_args()
    
    print("🧪 SuperLLM Integration Test")
    print("=" * 50)
    
    # Update token if provided
    if args.firebase_token:
        if args.update_config:
            if not update_token_in_config(args.firebase_token):
                sys.exit(1)
        else:
            print("💡 Token provided but --update-config not specified.")
            print("   The token will be used for this test only.")
    
    # Run tests
    success = True
    
    # Test 1: Configuration
    if not test_configuration():
        success = False
    
    # Test 2: Client initialization
    client = test_client_initialization()
    if client is None:
        success = False
    
    # Test 3: Factory integration
    if not test_factory_integration():
        success = False
    
    # Test 4: API call (only if client was created successfully)
    if client is not None:
        if not test_api_call(client, args.prompt):
            success = False
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! SuperLLM integration is working correctly.")
        print("\n📝 To use SuperLLM provider:")
        print("   1. Set 'provider': 'superllm' in your configuration")
        print("   2. Ensure your Firebase token is valid")
        print("   3. Make sure SuperLLM server is running")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Setup checklist:")
        print("   □ SuperLLM server is running (http://localhost:8000)")
        print("   □ You have authenticated via SuperLLM web interface")
        print("   □ Firebase token is configured in sutraknowledge config")
        print("   □ Network connectivity to SuperLLM server")
        
        sys.exit(1)


if __name__ == "__main__":
    main()
