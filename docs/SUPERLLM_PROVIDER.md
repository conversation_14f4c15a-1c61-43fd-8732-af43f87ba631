# SuperLLM Provider for SutraKnowledge

This document explains how to set up and use the SuperLLM provider in SutraKnowledge. The SuperLLM provider allows SutraKnowledge to communicate with the SuperLLM API server, which provides access to multiple LLM providers through a unified interface.

## Overview

The SuperLLM provider workflow:

1. **Authentication**: User authenticates via SuperLLM web interface
2. **Token Retrieval**: User obtains Firebase authentication token
3. **Configuration**: <PERSON><PERSON> is configured in SutraKnowledge
4. **API Communication**: SutraKnowledge uses token to make API calls to SuperLLM

## Prerequisites

- SuperLLM server running and accessible
- SuperLLM web interface for authentication
- Valid Firebase authentication token

## Setup Instructions

### 1. Start SuperLLM Server

Ensure the SuperLLM server is running. By default, it runs on `http://localhost:8000`.

```bash
# In the SuperLLM project directory
docker-compose up -d
# or
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 2. Authenticate with SuperLLM

Use the built-in authentication command to securely store your Firebase token:

```bash
# Interactive authentication (recommended)
sutra auth login

# With automatic web interface opening
sutra auth login --auto-open

# With custom endpoints
sutra auth login --api-endpoint http://localhost:8000 --web-url http://localhost:3000

# With token directly (not recommended for security)
sutra auth login --token YOUR_FIREBASE_TOKEN_HERE
```

The authentication process:
1. Opens SuperLLM web interface (if `--auto-open` is used)
2. Prompts you to sign in and copy your Firebase token
3. Validates the token with the SuperLLM API
4. Securely stores the token in `~/.sutra/auth/` (encrypted)

### 3. Configure SutraKnowledge (Optional)

The SuperLLM provider works without configuration changes, but you can customize settings:

```json
{
  "llm": {
    "provider": "superllm",
    "superllm": {
      "api_endpoint": "http://localhost:8000",
      "default_model": "gpt-3.5-turbo",
      "default_provider": "openai"
    }
  }
}
```

#### Configuration Options

- `api_endpoint`: URL of the SuperLLM API server (default: http://localhost:8000)
- `default_model`: Default model to use for requests (default: gpt-3.5-turbo)
- `default_provider`: Default provider to use (default: openai)
- `firebase_token`: **Not recommended** - use `sutra auth login` instead

### 4. Verify Authentication

Check your authentication status:

```bash
# Check all providers
sutra auth status

# Check specific provider
sutra auth status --provider superllm

# Test authentication
sutra auth test --provider superllm
```

## Usage

Once authenticated, SutraKnowledge will automatically use the SuperLLM provider when `provider` is set to `"superllm"` in the configuration.

### Authentication Commands

```bash
# Login to SuperLLM
sutra auth login

# Check authentication status
sutra auth status

# Test authentication
sutra auth test

# Logout (remove token)
sutra auth logout

# Clear all tokens
sutra auth clear
```

### Example Usage

```python
from sutraknowledge.src.services.llm_clients.llm_factory import llm_client_factory

# Create client (will use SuperLLM if configured and authenticated)
client = llm_client_factory()

# Make a request
response = client.call_llm("Hello, how are you?")
print(response)
```

## Available Models and Providers

The SuperLLM provider supports all models and providers available in your SuperLLM server configuration. Common options include:

### Providers
- `openai` - OpenAI GPT models
- `anthropic` - Anthropic Claude models
- `gcp_vertex` - Google Cloud Vertex AI models
- `aws_bedrock` - AWS Bedrock models
- `deepseek` - DeepSeek models

### Models
- `gpt-3.5-turbo`, `gpt-4`, `gpt-4-turbo` (OpenAI)
- `claude-3-sonnet`, `claude-3-haiku`, `claude-3-opus` (Anthropic)
- `gemini-pro`, `gemini-pro-vision` (Google)
- And more depending on your SuperLLM server configuration

## Troubleshooting

### Common Issues

#### 1. Authentication Failed
```
SuperLLM authentication failed. Please run 'sutra auth login' to re-authenticate with SuperLLM.
```

**Solution**: Your Firebase token has expired or is invalid. Run `sutra auth login` to re-authenticate.

#### 2. Connection Error
```
Failed to connect to SuperLLM API
```

**Solution**: 
- Check that SuperLLM server is running
- Verify the `api_endpoint` in your configuration
- Check network connectivity

#### 3. Rate Limit Exceeded
```
SuperLLM rate limit exceeded. Please try again later.
```

**Solution**: Wait for the rate limit to reset, or check your SuperLLM account limits.

#### 4. Token Not Found
```
SuperLLM Firebase token not found. Please run 'sutra auth login' to authenticate with SuperLLM.
```

**Solution**: Run `sutra auth login` to authenticate and store your token securely.

### Debug Mode

Enable debug logging to see detailed API requests:

```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

### Manual API Testing

Test the SuperLLM API directly:

```bash
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "model": "gpt-3.5-turbo",
    "provider": "openai"
  }'
```

## Security Considerations

- **Token Security**: Keep your Firebase token secure and don't commit it to version control
- **Token Rotation**: Regularly rotate your authentication tokens
- **Network Security**: Use HTTPS in production environments
- **Access Control**: Ensure proper access controls are configured in SuperLLM

## Advanced Configuration

### Custom Timeout

The SuperLLM client uses a 60-second timeout by default. This is not currently configurable but can be modified in the client code if needed.

### Multiple Environments

You can configure different SuperLLM endpoints for different environments:

```json
{
  "llm": {
    "superllm": {
      "api_endpoint": "https://superllm.production.com",
      "firebase_token": "production_token"
    }
  }
}
```

### Provider-Specific Settings

The SuperLLM provider will use the `default_model` and `default_provider` for all requests. To use different models or providers, you would need to modify the SuperLLM client or use the SuperLLM API directly.

## Support

For issues related to:
- **SuperLLM Provider**: Check this documentation and the test script
- **SuperLLM Server**: Refer to SuperLLM project documentation
- **SutraKnowledge**: Refer to main SutraKnowledge documentation

## API Reference

### SuperLLMClient Class

```python
class SuperLLMClient(LLMClientBase):
    def __init__(self):
        """Initialize with configuration from config.llm.superllm"""
    
    def call_llm(self, prompt: str) -> str:
        """
        Send prompt to SuperLLM API and return response.
        
        Args:
            prompt: Input text prompt
            
        Returns:
            Generated response text
            
        Raises:
            Exception: If API call fails
        """
```

### Configuration Schema

```python
@dataclass
class SuperLLMConfig:
    api_endpoint: str      # SuperLLM API URL
    firebase_token: str    # Firebase authentication token
    default_model: str     # Default model name
    default_provider: str  # Default provider name
```
