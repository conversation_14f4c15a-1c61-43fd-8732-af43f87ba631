# Sutra Knowledge CLI - Distribution Guide

This guide explains how to distribute and install the Sutra Knowledge CLI for end users.

## 📦 Distribution Architecture

### **End User Installation**
```
~/.sutra/
├── bin/sutra                    # Executable CLI
├── config/
│   ├── system.json             # System configuration
│   └── parsers.json            # Parser configuration
├── build/                      # Tree-sitter parsers
│   ├── Linux/                  # Linux .so files
│   └── Darwin/                 # macOS .dylib files
├── models/                     # ML models
│   └── all-MiniLM-L6-v2/
│       ├── model.onnx
│       └── tokenizer.json
├── data/                       # User data
└── logs/                       # Application logs
```

### **Developer Environment**
```
project/
├── src/parser/
│   ├── config/parsers.json     # Parser config
│   └── build/                  # Parser libraries
├── models/all-MiniLM-L6-v2/    # ML models
├── configs/local.json          # Development config
└── data/                       # Development data
```

## 🚀 Installation Methods

### **One-Line Installation (Recommended)**
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/sutragraph/models/main/install.sh)"
```

### **Manual Installation**
1. Download files from [GitHub Releases](https://github.com/sutragraph/models/releases)
2. Extract to `~/.sutra/`
3. Add `~/.sutra/bin` to PATH
4. Set `SUTRAKNOWLEDGE_CONFIG=~/.sutra/config/system.json`

## 📋 Release Preparation

### **1. Package Release Files**
```bash
# Run the packaging script
bash scripts/package_release.sh

# This creates:
# - dist/tree-sitter-build.tar.gz
# - dist/all-MiniLM-L6-v2.tar.gz  
# - dist/sutra (executable)
```

### **2. Upload to GitHub Releases**
```bash
# Create release and upload files
gh release create v0.1 dist/* \
  --title "Sutra Knowledge CLI v0.1" \
  --notes "Initial release of Sutra Knowledge CLI"
```

### **3. Test Installation**
```bash
# Test the installation script
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/sutragraph/models/main/install.sh)"

# Verify installation
sutra --help
```

## 🔧 Configuration System

### **Automatic Configuration Detection**
The CLI automatically detects the environment:

- **Installed**: Uses `~/.sutra/config/system.json`
- **Development**: Uses `configs/local.json`

### **Environment Variables**
- `SUTRAKNOWLEDGE_CONFIG`: Path to configuration file
- `PATH`: Must include `~/.sutra/bin` for installed version

## 📁 File Structure Details

### **GitHub Release Assets**
```
v0.1/
├── sutra                       # Main executable
├── tree-sitter-build.tar.gz   # Parser libraries
│   └── build/
│       ├── Linux/              # .so files
│       └── Darwin/             # .dylib files
└── all-MiniLM-L6-v2.tar.gz   # ML models
    └── all-MiniLM-L6-v2/
        ├── model.onnx
        └── tokenizer.json
```

### **Installation Script Features**
- ✅ System detection (Linux/macOS)
- ✅ Prerequisite checking (curl, tar)
- ✅ Automatic directory creation
- ✅ File download and extraction
- ✅ Configuration generation
- ✅ PATH setup
- ✅ Installation verification

## 🛠️ Development vs Production

### **Development Mode**
- Uses project directory files
- Configuration: `configs/local.json`
- Data stored in `data/`
- Models in `models/`

### **Production Mode**
- Uses `~/.sutra/` directory
- Configuration: `~/.sutra/config/system.json`
- Data stored in `~/.sutra/data/`
- Models in `~/.sutra/models/`

## 🗑️ Uninstallation

### **One-Line Uninstall**
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/sutragraph/models/main/uninstall.sh)"
```

### **Manual Uninstall**
```bash
# Remove installation directory
rm -rf ~/.sutra

# Remove from shell configuration
# Edit ~/.bashrc or ~/.zshrc and remove Sutra lines
```

## 🔄 Updates

### **Update Process**
1. Run uninstall script
2. Run install script with new version
3. Or manually replace files in `~/.sutra/`

### **Version Management**
- Update `RELEASE_TAG` in install script
- Create new GitHub release
- Update documentation

## 🧪 Testing

### **Test Installation**
```bash
# Test in clean environment
docker run -it ubuntu:latest bash
curl -fsSL https://raw.githubusercontent.com/sutragraph/models/main/install.sh | bash
```

### **Test Functionality**
```bash
# After installation
sutra --help
sutra --version
cd /path/to/repo && sutra
```

## 📝 Maintenance

### **Regular Tasks**
- Update parser libraries for new languages
- Update ML models for better performance
- Update installation script for new features
- Test on different operating systems

### **Monitoring**
- Track download statistics
- Monitor installation success rates
- Collect user feedback
- Update documentation

## 🔗 Links

- **GitHub Repository**: https://github.com/sutragraph/models
- **Releases**: https://github.com/sutragraph/models/releases
- **Installation Script**: https://raw.githubusercontent.com/sutragraph/models/main/install.sh
- **Uninstall Script**: https://raw.githubusercontent.com/sutragraph/models/main/uninstall.sh
