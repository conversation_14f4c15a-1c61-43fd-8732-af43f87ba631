#!/bin/bash
# Sutra Knowledge CLI - Complete Installation Script
# Supports macOS and Linux (Ubuntu/Debian, CentOS/RHEL, Arch)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/sutraknowledge"
DATA_DIR="${INSTALL_DIR}/data"
MODELS_DIR="${INSTALL_DIR}/models"
LOG_DIR="/var/log/sutraknowledge"
BIN_DIR="/usr/local/bin"
CONFIG_DIR="/etc/sutraknowledge"
USER_CONFIG_DIR="${HOME}/.config/sutraknowledge"

# Python version requirements
PYTHON_MIN_VERSION="3.8"
REQUIRED_PYTHON_PACKAGES=(
    "pip"
    "setuptools"
    "wheel"
    "virtualenv"
)

# System packages for different distributions
UBUNTU_PACKAGES=(
    "python3"
    "python3-pip"
    "python3-dev"
    "python3-venv"
    "build-essential"
    "cmake"
    "pkg-config"
    "libssl-dev"
    "libffi-dev"
    "sqlite3"
    "libsqlite3-dev"
    "git"
    "curl"
    "wget"
    "unzip"
    "libcap2-bin"
)

CENTOS_PACKAGES=(
    "python3"
    "python3-pip"
    "python3-devel"
    "gcc"
    "gcc-c++"
    "make"
    "cmake"
    "pkgconfig"
    "openssl-devel"
    "libffi-devel"
    "sqlite"
    "sqlite-devel"
    "git"
    "curl"
    "wget"
    "unzip"
    "libcap-devel"
)

ARCH_PACKAGES=(
    "python"
    "python-pip"
    "base-devel"
    "cmake"
    "pkgconf"
    "openssl"
    "libffi"
    "sqlite"
    "git"
    "curl"
    "wget"
    "unzip"
    "libcap"
)

MACOS_PACKAGES=(
    "python3"
    "cmake"
    "pkg-config"
    "openssl"
    "libffi"
    "sqlite3"
    "git"
    "wget"
)

# Functions
print_header() {
    echo -e "${BLUE}=================================="
    echo -e "Sutra Knowledge CLI Installer"
    echo -e "==================================${NC}"
    echo ""
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "Detected macOS"
    elif [[ -f /etc/os-release ]]; then
        . /etc/os-release
        case $ID in
            ubuntu|debian)
                OS="ubuntu"
                print_success "Detected Ubuntu/Debian-based system"
                ;;
            centos|rhel|fedora)
                OS="centos"
                print_success "Detected CentOS/RHEL/Fedora-based system"
                ;;
            arch|manjaro)
                OS="arch"
                print_success "Detected Arch-based system"
                ;;
            *)
                print_warning "Unknown Linux distribution: $ID"
                print_warning "Attempting Ubuntu/Debian installation..."
                OS="ubuntu"
                ;;
        esac
    else
        print_error "Unsupported operating system"
        exit 1
    fi
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        echo "Usage: sudo bash install.sh"
        exit 1
    fi
}

check_python_version() {
    print_step "Checking Python version..."
    
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
            print_success "Python $PYTHON_VERSION found (>= $PYTHON_MIN_VERSION)"
            PYTHON_CMD="python3"
        else
            print_error "Python $PYTHON_VERSION found, but >= $PYTHON_MIN_VERSION required"
            exit 1
        fi
    else
        print_error "Python 3 not found"
        exit 1
    fi
}

install_system_packages() {
    print_step "Installing system packages..."
    
    case $OS in
        macos)
            # Check if Homebrew is installed
            if ! command -v brew >/dev/null 2>&1; then
                print_step "Installing Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            
            # Update Homebrew
            brew update
            
            # Install packages
            for package in "${MACOS_PACKAGES[@]}"; do
                if ! brew list "$package" >/dev/null 2>&1; then
                    print_step "Installing $package..."
                    brew install "$package"
                else
                    print_success "$package already installed"
                fi
            done
            ;;
            
        ubuntu)
            apt-get update -y
            for package in "${UBUNTU_PACKAGES[@]}"; do
                print_step "Installing $package..."
                apt-get install -y "$package"
            done
            ;;
            
        centos)
            if command -v dnf >/dev/null 2>&1; then
                DNF_CMD="dnf"
            else
                DNF_CMD="yum"
            fi
            
            $DNF_CMD update -y
            for package in "${CENTOS_PACKAGES[@]}"; do
                print_step "Installing $package..."
                $DNF_CMD install -y "$package"
            done
            ;;
            
        arch)
            pacman -Syu --noconfirm
            for package in "${ARCH_PACKAGES[@]}"; do
                print_step "Installing $package..."
                pacman -S --noconfirm "$package"
            done
            ;;
    esac
    
    print_success "System packages installed"
}

create_directories() {
    print_step "Creating directory structure..."
    
    # Create main directories
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$DATA_DIR/sessions"
    mkdir -p "$DATA_DIR/file_changes"
    mkdir -p "$DATA_DIR/edits"
    mkdir -p "$MODELS_DIR"
    mkdir -p "$INSTALL_DIR/parser_results"
    mkdir -p "$LOG_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$USER_CONFIG_DIR"
    mkdir -p "$INSTALL_DIR/src"
    mkdir -p "$INSTALL_DIR/configs"
    mkdir -p "$INSTALL_DIR/parser"
    mkdir -p "$INSTALL_DIR/certs"
    
    # Set permissions
    chmod 755 "$INSTALL_DIR"
    chmod 755 "$DATA_DIR"
    chmod 755 "$MODELS_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$CONFIG_DIR"
    
    # Create log file with proper permissions
    touch "$LOG_DIR/sutraknowledge.log"
    chmod 644 "$LOG_DIR/sutraknowledge.log"
    
    print_success "Directory structure created"
}

setup_python_environment() {
    print_step "Setting up Python environment..."
    
    # Create virtual environment
    cd "$INSTALL_DIR"
    $PYTHON_CMD -m venv venv
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip setuptools wheel
    
    print_success "Python virtual environment created"
}

install_python_dependencies() {
    print_step "Installing Python dependencies..."
    
    # Activate virtual environment
    cd "$INSTALL_DIR"
    source venv/bin/activate
    
    # Core dependencies
    pip install loguru pydantic numpy
    
    # ML and AI dependencies
    pip install onnxruntime tokenizers transformers
    
    # Database dependencies
    pip install sqlite-vec
    
    # LLM client dependencies
    pip install boto3 botocore
    pip install google-cloud-aiplatform google-auth
    pip install anthropic
    pip install google-genai
    
    # Parser dependencies
    pip install tree-sitter PyYAML
    
    # Build dependencies
    pip install pyinstaller
    
    # Additional utilities
    pip install requests urllib3 certifi
    
    print_success "Python dependencies installed"
}

create_config_file() {
    print_step "Creating configuration file..."
    
    cat > "$CONFIG_DIR/config.json" << 'EOF'
{
  "database": {
    "knowledge_graph_db": "/opt/sutraknowledge/data/knowledge_graph.db",
    "embeddings_db": "/opt/sutraknowledge/data/knowledge_graph_embeddings.db",
    "connection_timeout": 60,
    "max_retry_attempts": 5,
    "batch_size": 2000,
    "enable_indexing": true,
    "create_tables": true,
    "enable_wal_mode": true
  },
  "storage": {
    "data_dir": "/opt/sutraknowledge/data",
    "sessions_dir": "/opt/sutraknowledge/data/sessions",
    "file_changes_dir": "/opt/sutraknowledge/data/file_changes",
    "file_edits_dir": "/opt/sutraknowledge/data/edits",  
    "parser_results_dir": "/opt/sutraknowledge/parser_results",
    "models_dir": "/opt/sutraknowledge/models"
  },
  "embedding": {
    "model_name": "all-MiniLM-L6-v2",
    "model_path": "/opt/sutraknowledge/models/all-MiniLM-L6-v2",
    "max_tokens": 512,
    "overlap_tokens": 50,
    "similarity_threshold": 0.7,
    "enable_optimization": true
  },
  "logging": {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    "log_file": "/var/log/sutraknowledge/sutraknowledge.log"
  },
  "llm": {
    "provider": "gemini",
    "llama_model_id": "meta/llama-4-maverick-17b-128e-instruct-maas",
    "claude_model": "claude-3-7-sonnet@20250219",
    "gemini_model": "gemini-2.5-flash",
    "aws": {
      "model_id": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
      "access_key_id": "",
      "secret_access_key": "",
      "region": "us-east-2"
    },
    "gcp": {
      "api_key": "",
      "project_id": "",
      "location": "us-east5",
      "llm_endpoint": "https://us-east5-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/us-east5/endpoints/openapi/chat/completions"
    },
    "superllm": {
      "api_endpoint": "http://localhost:8000",
      "firebase_token": "",
      "default_model": "gpt-3.5-turbo",
      "default_provider": "openai"
    }
  }
}
EOF
    
    chmod 644 "$CONFIG_DIR/config.json"
    print_success "Configuration file created at $CONFIG_DIR/config.json"
}

create_systemd_service() {
    print_step "Creating systemd service (Linux only)..."
    
    if [[ "$OS" != "macos" ]]; then
        cat > /etc/systemd/system/sutraknowledge.service << EOF
[Unit]
Description=Sutra Knowledge CLI Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$INSTALL_DIR
Environment=PATH=$INSTALL_DIR/venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=$INSTALL_DIR/venv/bin/python sutra_cli.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sutraknowledge

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        print_success "Systemd service created"
    else
        print_success "Skipping systemd service creation on macOS"
    fi
}

create_launcher_script() {
    print_step "Creating launcher script..."
    
    cat > "$BIN_DIR/sutra" << EOF
#!/bin/bash
# Sutra Knowledge CLI Launcher Script

# Set environment variables
export SUTRA_CONFIG_PATH="$CONFIG_DIR/config.json"
export SUTRA_DATA_DIR="$DATA_DIR"
export SUTRA_MODELS_DIR="$MODELS_DIR"

# Change to installation directory
cd "$INSTALL_DIR"

# Activate virtual environment and run
source venv/bin/activate
exec python sutra_cli.py "\$@"
EOF
    
    chmod +x "$BIN_DIR/sutra"
    print_success "Launcher script created at $BIN_DIR/sutra"
}

download_models() {
    print_step "Downloading ML models..."
    
    cd "$MODELS_DIR"
    
    # Download embedding model (example - adjust URL as needed)
    if [[ ! -d "all-MiniLM-L6-v2" ]]; then
        print_step "Downloading all-MiniLM-L6-v2 model..."
        # This would typically download from Hugging Face or similar
        # For now, create placeholder structure
        mkdir -p all-MiniLM-L6-v2
        echo "# Model will be downloaded on first use" > all-MiniLM-L6-v2/README.md
        print_success "Model directory structure created"
    else
        print_success "Model already exists"
    fi
}

setup_permissions() {
    print_step "Setting up permissions..."
    
    # Set ownership (if not running as root user's install)
    chown -R root:root "$INSTALL_DIR"
    chown -R root:root "$LOG_DIR"
    chown -R root:root "$CONFIG_DIR"
    
    # Set specific permissions
    chmod -R 755 "$INSTALL_DIR"
    chmod -R 644 "$INSTALL_DIR"/*.py 2>/dev/null || true
    chmod -R 755 "$INSTALL_DIR"/venv/bin/* 2>/dev/null || true
    chmod 755 "$LOG_DIR"
    chmod 644 "$LOG_DIR"/*.log 2>/dev/null || true
    
    # Set capabilities for the launcher script (Linux only)
    if [[ "$OS" != "macos" ]] && command -v setcap >/dev/null 2>&1; then
        print_step "Setting capabilities for enhanced permissions..."
        setcap cap_dac_override,cap_fowner,cap_chown+ep "$BIN_DIR/sutra" || \
        print_warning "Could not set capabilities - binary will run with user permissions"
    fi
    
    print_success "Permissions configured"
}

create_uninstall_script() {
    print_step "Creating uninstall script..."
    
    cat > "$INSTALL_DIR/uninstall.sh" << 'EOF'
#!/bin/bash
# Sutra Knowledge CLI Uninstaller

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Uninstalling Sutra Knowledge CLI...${NC}"

# Stop and disable service (Linux only)
if [[ "$OSTYPE" != "darwin"* ]] && systemctl is-active --quiet sutraknowledge 2>/dev/null; then
    echo "Stopping sutraknowledge service..."
    systemctl stop sutraknowledge
    systemctl disable sutraknowledge
    rm -f /etc/systemd/system/sutraknowledge.service
    systemctl daemon-reload
fi

# Remove directories and files
echo "Removing installation files..."
rm -rf /opt/sutraknowledge
rm -rf /var/log/sutraknowledge
rm -rf /etc/sutraknowledge
rm -f /usr/local/bin/sutra

# Remove user config (optional)
read -p "Remove user configuration directory? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf ~/.config/sutraknowledge
    echo "User configuration removed"
fi

echo -e "${GREEN}Sutra Knowledge CLI uninstalled successfully${NC}"
EOF
    
    chmod +x "$INSTALL_DIR/uninstall.sh"
    print_success "Uninstall script created at $INSTALL_DIR/uninstall.sh"
}

print_final_instructions() {
    echo ""
    echo -e "${GREEN}=================================="
    echo -e "Installation Complete!"
    echo -e "==================================${NC}"
    echo ""
    echo "Installation Details:"
    echo "  • Installation Directory: $INSTALL_DIR"
    echo "  • Data Directory: $DATA_DIR"
    echo "  • Models Directory: $MODELS_DIR"
    echo "  • Configuration: $CONFIG_DIR/config.json"
    echo "  • Logs: $LOG_DIR/sutraknowledge.log"
    echo ""
    echo "Usage:"
    echo "  • Run: sutra"
    echo "  • Help: sutra --help"
    echo "  • Config: Edit $CONFIG_DIR/config.json"
    echo ""
    echo "Next Steps:"
    echo "  1. Add your API keys to $CONFIG_DIR/config.json"
    echo "  2. Copy your sutra_cli.py and source files to $INSTALL_DIR"
    echo "  3. Run the build script: cd $INSTALL_DIR && python build_full.py"
    echo "  4. Test the installation: sutra --help"
    echo ""
    if [[ "$OS" != "macos" ]]; then
        echo "Service Management (Linux):"
        echo "  • Start: systemctl start sutraknowledge"
        echo "  • Stop: systemctl stop sutraknowledge"
        echo "  • Enable: systemctl enable sutraknowledge"
        echo "  • Status: systemctl status sutraknowledge"
        echo ""
    fi
    echo "Uninstall:"
    echo "  • Run: $INSTALL_DIR/uninstall.sh"
    echo ""
    echo -e "${YELLOW}Remember to add your API credentials to the configuration file!${NC}"
}

# Main installation process
main() {
    print_header
    
    # Pre-installation checks
    detect_os
    check_root
    check_python_version
    
    # Installation steps
    install_system_packages
    create_directories
    setup_python_environment
    install_python_dependencies
    create_config_file
    create_systemd_service
    create_launcher_script
    download_models
    setup_permissions
    create_uninstall_script
    
    # Final instructions
    print_final_instructions
    
    print_success "Installation completed successfully!"
}

# Handle script interruption
trap 'print_error "Installation interrupted"; exit 1' INT TERM

# Run main function
main "$@"