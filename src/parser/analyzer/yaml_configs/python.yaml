# Python Configuration
# Clean, maintainable configuration for Python analysis

language: python

# Skip rules for filtering elements
skip_rules:
  function:
    - type: name_equals
      value: __init__
  constructor:
    - type: name_not_equals
      value: __init__

# Structural elements to extract
elements:
  function:
    query: |
      (function_definition
        name: (identifier) @name
        parameters: (parameters) @parameters
      ) @element
    metadata_checks:
      is_async:
        type: text_contains
        capture: element
        value: "async def"
      has_decorators:
        type: exists
        capture: decorators

  class:
    query: |
      (class_definition
        name: (identifier) @name
        superclasses: (argument_list)? @superclasses
      ) @element
    metadata_checks:
      has_base_classes:
        type: exists
        capture: superclasses

  constructor:
    query: |
      (function_definition
        name: (identifier) @name
        parameters: (parameters) @parameters
      ) @element
      (#eq? @name "__init__")

# Relationships to extract
relationships:
  imports:
    query: |
      (import_statement
        name: (dotted_name) @target
      )
      (import_from_statement
        module_name: (dotted_name) @target
      )
    target_capture: target
    target_type: file

  calls:
    query: |
      ; Direct function calls (e.g., add(1, 2))
      (call
        function: (identifier) @target
      )

      ; Attribute calls (e.g., obj.method(), Class.static_method())
      (call
        function: (attribute
          object: (identifier) @object
          attribute: (identifier) @method
        )
      )
    target_capture: target
    target_type: function

  extends:
    query: |
      (class_definition
        superclasses: (argument_list
          (identifier) @target
        )
      )
    target_capture: target
    target_type: class

  instantiates:
    query: |
      (call
        function: (identifier) @target
      )
    target_capture: target
    target_type: class
