# JavaScript Configuration
# Clean, maintainable configuration for JavaScript analysis

language: javascript

# Skip rules for filtering elements
skip_rules:
  method:
    - type: name_equals
      value: constructor
  constructor:
    - type: name_not_equals
      value: constructor

# Structural elements to extract
elements:
  function:
    query: |
      ; Regular function declarations
      (function_declaration
        name: (identifier) @name
        parameters: (formal_parameters) @parameters
      ) @element

      ; Arrow functions assigned to variables (with formal parameters)
      (variable_declarator
        name: (identifier) @name
        value: (arrow_function
          parameters: (formal_parameters)
        )
      ) @element

      ; Arrow functions assigned to variables (single parameter)
      (variable_declarator
        name: (identifier) @name
        value: (arrow_function
          parameter: (identifier)
        )
      ) @element

      ; Function expressions assigned to variables
      (variable_declarator
        name: (identifier) @name
        value: (function_expression
          parameters: (formal_parameters)
        )
      ) @element

      ; Object method shorthand (function expressions)
      (pair
        key: (property_identifier) @name
        value: (function_expression
          parameters: (formal_parameters)
        )
      ) @element

      ; Object method shorthand (arrow functions)
      (pair
        key: (property_identifier) @name
        value: (arrow_function
          parameters: (formal_parameters)
        )
      ) @element

    metadata_checks:
      is_async:
        type: text_contains
        capture: element
        value: "async"
      is_arrow:
        type: text_contains
        capture: element
        value: "=>"

  method:
    query: |
      ; Class method definitions (all methods, constructors filtered in post-processing)
      (method_definition
        name: (property_identifier) @name
        parameters: (formal_parameters) @parameters
      ) @element

    metadata_checks:
      is_async:
        type: text_contains
        capture: element
        value: "async"

  class:
    query: |
      (class_declaration
        name: (identifier) @name
      ) @element
    metadata_checks:
      has_constructor:
        type: exists
        capture: constructor

  constructor:
    query: |
      ; Match method definitions (constructors filtered in post-processing)
      (method_definition
        name: (property_identifier) @name
        parameters: (formal_parameters) @parameters
      ) @element

# Relationships to extract
relationships:
  imports:
    query: |
      (import_statement
        source: (string) @target
      )
      (call_expression
        function: (identifier) @func
        arguments: (arguments
          (string) @target
        )
      )
      (#eq? @func "require")
    target_capture: target
    target_type: file

  calls:
    query: |
      ; Direct function calls (e.g., add(1, 2))
      (call_expression
        function: (identifier) @target
      )

      ; Member expression calls (e.g., obj.method(), Class.staticMethod())
      (call_expression
        function: (member_expression
          object: (identifier) @object
          property: (property_identifier) @method
        )
      )

      ; Chained member calls (e.g., obj.prop.method())
      (call_expression
        function: (member_expression
          object: (member_expression) @object
          property: (property_identifier) @method
        )
      )
    target_capture: target
    target_type: function

  extends:
    query: |
      (class_declaration
        name: (identifier)
        body: (class_body)
      ) @element
    target_capture: target
    target_type: class

  instantiates:
    query: |
      (new_expression
        constructor: (identifier) @target
      )
    target_capture: target
    target_type: class
