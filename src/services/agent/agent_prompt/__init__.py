"""
Agent prompt module with clean, modular prompt building system.
"""

from .prompt_builder import Prompt<PERSON>uilder, PromptConfiguration
from .prompt_templates import (
    BASE_PROMPT_TEMPLATE,
    TOOLS_SECTION_TEMPLATE,
    WORKFLOW_SECTION_TEMPLATE,
    RESPONSE_FORMAT_TEMPLATE,
    QUERY_IMPROVEMENT_TEMPLATE,
    get_prompt_template,
    build_placeholder_map,
    get_query_improvement_prompt,
    build_dynamic_response_format,
    build_dynamic_error_handling,
    build_dynamic_context_section
)
from .tool_specific_instructions import (
    get_tool_specific_instructions,
    build_dynamic_tool_instructions,
    determine_tool_context
)
from .dynamic_prompt_builder import (
    build_dynamic_action_instructions,
    enhance_prompt_with_dynamic_instructions
)

__all__ = [
    # Main prompt building
    "PromptBuilder",
    "PromptConfiguration",

    # Templates
    "BASE_PROMPT_TEMPLATE",
    "TOOLS_SECTION_TEMPLATE",
    "WORKFLOW_SECTION_TEMPLATE",
    "RESPONSE_FORMAT_TEMPLATE",
    "QUERY_IMPROVEMENT_TEMPLATE",
    "get_prompt_template",
    "build_placeholder_map",
    "get_query_improvement_prompt",
    "build_dynamic_response_format",
    "build_dynamic_error_handling",
    "build_dynamic_context_section",

    # Tool-specific instructions
    "get_tool_specific_instructions",
    "build_dynamic_tool_instructions",
    "determine_tool_context",

    # Dynamic prompt building
    "build_dynamic_action_instructions",
    "enhance_prompt_with_dynamic_instructions"
]
