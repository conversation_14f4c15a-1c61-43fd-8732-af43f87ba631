"""
Guidance message templates for different search scenarios.
These messages provide context-aware guidance to the LLM based on the search results.
"""

from .constants import SEARCH_CONFIG

# Helper to get display values from config
CHUNKING_THRESHOLD = SEARCH_CONFIG["chunking_threshold"]
CHUNK_SIZE = SEARCH_CONFIG["chunk_size"]

# Semantic Search Guidance Messages - Simplified for Batch-Based Chunk Delivery
SEMANTIC_SEARCH_MESSAGES = {
    # First batch of results (7 chunks delivered)
    "first_batch": (
        "SEMANTIC DISCOVERY: Found {total_nodes} chunk-specific code snippets across the codebase. "
        "Showing first batch of {delivered_count} chunks with matched lines only. "
        "Pattern analysis: Compare implementations to identify common patterns and architectural decisions. "
        "Continued access: Use fetch_next_code: true to see more chunks, or analyze current patterns to understand the codebase."
    ),
    # Subsequent batches (more chunks available)
    "next_batch": (
        "SEMANTIC DISCOVERY: Continuing exploration - batch {batch_number} of chunk-specific code snippets. "
        "Showing {delivered_count} more chunks with matched lines. "
        "Pattern discovery: Build understanding across implementations and identify key patterns. "
        "Continued access: Use fetch_next_code: true for more chunks, or synthesize findings from current results."
    ),
    # Final batch (no more chunks available)
    "final_batch": (
        "SEMANTIC DISCOVERY: Final batch of chunk-specific code snippets. "
        "Showing last {delivered_count} chunks - exploration complete with {total_nodes} total chunks analyzed. "
        "Synthesis time: Compare all implementations to identify patterns, architectural decisions, and best practices across the codebase."
    ),
    # Single batch (all results fit in one batch)
    "single_batch": (
        "SEMANTIC DISCOVERY: Found {total_nodes} chunk-specific code snippets across the codebase. "
        "All results delivered in single batch - showing matched lines only. "
        "Pattern analysis: Compare implementations to identify common patterns, architectural connections, and design decisions."
    ),
    # Edge case scenarios
    "no_results_found": (
        "SEMANTIC SEARCH: No matching patterns found in the codebase. "
        "Exploration strategies: Try broader terms, different concepts, or related functionality. "
        "Alternative: Use database queries with specific file/function names if you know what you're looking for."
    ),
    "node_missing_code_content": (
        "SEMANTIC DISCOVERY: Found matching node but code content unavailable. "
        "This suggests a metadata match - use database queries to fetch the actual implementation."
    ),
}

# Database Search Guidance Messages - Focus on Targeted Retrieval & Precise Analysis
DATABASE_SEARCH_MESSAGES = {
    # Multiple nodes, metadata only
    "multiple_nodes_metadata_only": (
        "DATABASE RETRIEVAL: Located {total_nodes} matching entities. "
        "Examine metadata to identify the exact files/functions you need. "
        "Targeted access: Use GET_CODE_FROM_FILE for complete files or GET_CODE_FROM_FILE_LINES for specific line ranges. "
        "Focus on precise retrieval of the code sections relevant to your task."
    ),
    # Single node, metadata only
    "single_node_metadata_only": (
        "DATABASE RETRIEVAL: Located 1 matching entity. "
        "Review metadata details and fetch the specific code you need for analysis or modification."
    ),
    # Multiple nodes, with code, no chunking needed
    "multiple_nodes_with_code_no_chunking": (
        "DATABASE RETRIEVAL: Retrieved {total_nodes} code entities. "
        "All files are manageable size (<{CHUNKING_THRESHOLD} lines). "
        "Targeted analysis: fetch_next_code: true for more entities, or examine current code for specific implementation details."
    ),
    # Multiple nodes, with code, no chunking needed - last node
    "multiple_nodes_with_code_no_chunking_last_node": (
        "DATABASE RETRIEVAL: Retrieved all {total_nodes} matching entities. "
        "Final entity ({current_node}/{total_nodes}) with {total_lines} lines shown. "
        "Complete analysis: You now have all requested code. Focus on the specific details needed for your task."
    ),
    # Multiple nodes, with code, some may need chunking
    "multiple_nodes_with_code_mixed_chunking": (
        "DATABASE RETRIEVAL: Retrieved {total_nodes} code entities (some large files). "
        "Large files will be delivered in {CHUNK_SIZE}-line segments for detailed analysis. "
        "Precise access: fetch_next_code: true for more entities, or examine current code for specific implementation details."
    ),
    # Single node, small code (no chunking)
    "single_node_small_code": (
        "DATABASE RETRIEVAL: Retrieved complete file ({total_lines} lines). "
        "Direct access: Full implementation provided. Examine the code for specific details, functions, or logic you need."
    ),
    # Single node, line-filtered code
    "single_node_line_filtered": (
        "DATABASE RETRIEVAL: Retrieved targeted section (lines {start_line}-{end_line} of {total_lines} total). "
        "Precise analysis: Focus on this specific code section. Use additional queries if you need more context or related code."
    ),
    # Single node, large code (chunking needed) - first chunk
    "single_node_large_code_first_chunk": (
        "DATABASE RETRIEVAL: Large file retrieved ({total_lines} lines). "
        "Delivering in segments - lines {chunk_start}-{chunk_end} (chunk {chunk_num}/{total_chunks}). "
        "Sequential access: Use fetch_next_code: true to get remaining segments of the current file. "
        "Detailed analysis: Examine this section for specific functions, logic, or implementation details you need. "
        "IMPORTANT: If you identify changes needed in this chunk, use git_diff tool FIRST before fetching next chunks."
    ),
    # Single node, large code (chunking needed) - subsequent chunks
    "single_node_large_code_subsequent_chunk": (
        "CHUNK DELIVERY: Continuing file analysis - chunk {chunk_num}/{total_chunks} "
        "(lines {chunk_start}-{chunk_end} of {total_lines} total). "
        "Sequential access: Use fetch_next_code: true to get remaining segments of the current file. "
        "Targeted review: Focus on specific functions, classes, or logic relevant to your task. "
        "IMPORTANT: If you identify changes needed in this chunk, use git_diff tool FIRST before fetching next chunks."
    ),
    # Single node, large code (chunking needed) - last chunk
    "single_node_large_code_last_chunk": (
        "RETRIEVAL COMPLETE: Final segment delivered - chunk {chunk_num}/{total_chunks} "
        "(lines {chunk_start}-{chunk_end} of {total_lines} total). "
        "Complete analysis: You now have the full file. Focus on the specific implementation details you need."
    ),
    # Multiple nodes, large code - last chunk with more nodes available
    "multiple_nodes_large_code_last_chunk_more_nodes": (
        "ENTITY COMPLETE: Finished retrieving entity {current_node}/{total_nodes} "
        "(completed chunk {chunk_num}/{total_chunks}). "
        "Continued access: fetch_next_code: true for next entity, or analyze current code for specific implementation details."
    ),
    # Multiple nodes, large code - last chunk of last node
    "multiple_nodes_large_code_last_chunk_last_node": (
        "RETRIEVAL COMPLETE: All {total_nodes} entities retrieved "
        "(final chunk {chunk_num}/{total_chunks} of last entity). "
        "Complete analysis: You now have all requested code. Focus on specific functions, logic, or details needed for your task."
    ),
    # Edge case scenarios
    "no_results_found": (
        "DATABASE SEARCH: No matching entities found for your query. "
        "Targeted alternatives: Check exact spelling, try related terms, or use specific file paths if known."
    ),
    "node_missing_code_content": (
        "DATABASE ACCESS: Entity located but code content unavailable. "
        "Try alternative queries or check if the file exists and is accessible."
    ),
}

# Sequential node guidance for multiple nodes with code - Generic for both search types
SEQUENTIAL_NODE_MESSAGES = {
    # Node with small code (no chunking)
    "node_small_code": (
        "NODE {node_index}/{total_nodes}: Complete file delivered ({total_lines} lines). "
        "Continue processing: fetch_next_code: true for more results, or analyze this implementation for your task."
    ),
    # Node with small code (no chunking) - last node
    "node_small_code_last_node": (
        "FINAL NODE {node_index}/{total_nodes}: Last file delivered ({total_lines} lines). "
        "All results processed - no more nodes available. "
        "Complete analysis: Review all implementations to accomplish your task."
    ),
    # Node with large code (chunking needed) - first chunk
    "node_large_code_first_chunk": (
        "NODE {node_index}/{total_nodes}: Large file ({total_lines} lines) - chunk {chunk_num}/{total_chunks} "
        "(lines {chunk_start}-{chunk_end}). "
        "Sequential access: Use fetch_next_code: true to get remaining chunks of this file. "
        "Incremental analysis: Review this section, more chunks will follow automatically. "
        "IMPORTANT: If you identify changes needed in this chunk, use git_diff tool FIRST before fetching next chunks."
    ),
    # Node with large code (chunking needed) - subsequent chunks
    "node_large_code_subsequent_chunk": (
        "CHUNK {chunk_num}/{total_chunks}: Node {node_index} continuation "
        "(lines {chunk_start}-{chunk_end} of {total_lines} total). "
        "Sequential access: Use fetch_next_code: true to get remaining chunks of this file. "
        "Progressive analysis: Continue reviewing, more chunks available for this file. "
        "IMPORTANT: If you identify changes needed in this chunk, use git_diff tool FIRST before fetching next chunks."
    ),
    # Node with large code (chunking needed) - last chunk
    "node_large_code_last_chunk": (
        "NODE COMPLETE: Node {node_index} final chunk {chunk_num}/{total_chunks} "
        "(lines {chunk_start}-{chunk_end} of {total_lines} total). "
        "Continue processing: fetch_next_code: true for next file, or analyze current implementation."
    ),
    # Node with large code (chunking needed) - last chunk of last node
    "node_large_code_last_chunk_last_node": (
        "ALL COMPLETE: Final chunk of last node {node_index} "
        "(chunk {chunk_num}/{total_chunks}, lines {chunk_start}-{chunk_end} of {total_lines} total). "
        "Processing finished: All files delivered. Complete your analysis and task implementation."
    ),
    # Node with no code content
    "node_no_code_content": (
        "NODE {node_index}/{total_nodes}: No code content available. "
        "This may be metadata-only or the code could not be retrieved. "
        "Continue processing: fetch_next_code: true for more results."
    ),
}
