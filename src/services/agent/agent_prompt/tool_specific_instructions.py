"""
Tool-specific dynamic instructions that are injected into prompts based on the tools being used.
This keeps the main prompt clean while providing detailed guidance when specific tools are needed.
"""

from typing import Dict, List, Optional, Any
from .constants import SEARCH_CONFIG

# Get display values from config
CHUNKING_THRESHOLD = SEARCH_CONFIG["chunking_threshold"]
CHUNK_SIZE = SEARCH_CONFIG["chunk_size"]


# Semantic Search Guidance - Simplified and focused
SEMANTIC_SEARCH_GUIDANCE = {
    "basic": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **DEEP VALIDATION REQUIRED** - for every function/variable you find, verify it exists and understand its complete signature
• **Check all dependencies** - when you find a function, examine what other functions it uses and verify they exist
• **Validate configurations** - look for environment variables, database pools, queues that need to be configured
• **Understand complete implementation** - don't just copy patterns, understand how they work and what they depend on
• **Never assume anything exists** - always search and verify before using any component
""",
    "with_chunking": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Large files chunked** into {CHUNK_SIZE}-line segments - use fetch_next_code: true to continue
• **Store actual code patterns** with line numbers when found
• **Look for implementation patterns** that can be reused for current and future tasks
""",
    "sequential_code": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Multiple implementations found** - pick one pattern and implement immediately
• **Don't collect all patterns** - implement with current findings
• **Use fetch_next_code: true** only if completely blocked and need more info
""",
    "sequential_code_last": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Enough patterns found** - implement immediately with current knowledge
• **Stop collecting** - use what you have to make progress
• **Implement now** rather than searching more
""",
    "single_code_node": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Single implementation found** - examine it thoroughly to understand all functions it uses
• **Validate all functions** - check their signatures, parameters, and dependencies
• **Implement immediately** if you have what you need, don't search for more patterns
""",
    "metadata_only": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Metadata found** - use database queries to get actual code
• **Focus on promising files** that might contain implementation patterns
• **Get code snippets** rather than just metadata
""",
    "no_results": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Nothing found** - functionality doesn't exist in project, create from scratch
• **Look for similar patterns** in codebase to understand how to implement
• **Use ls only if needed** to understand project structure before implementing new features
""",
}

# Database Search Guidance - Simplified
DATABASE_SEARCH_GUIDANCE = {
    "basic": f"""
=== DATABASE RETRIEVAL ===
• **Get exact function implementations** - use GET_CODE_FROM_FILE or GET_NODES_BY_EXACT_NAME
• **Validate function signatures** - understand parameters, return values, and dependencies
• **Check function usage** - use GET_FUNCTION_CALLERS/CALLEES to understand how functions are used
• **Focus on implementation details** needed for your specific changes
""",
    "with_chunking": f"""
=== DATABASE RETRIEVAL ===
• **Large files chunked** - use fetch_next_code: true for remaining segments
• **Store relevant code** with line numbers for implementation
""",
    "sequential_code": f"""
=== DATABASE RETRIEVAL ===
• **Multiple entities** - use fetch_next_code: true for next item
• **Store implementation details** for all relevant entities
""",
    "sequential_code_last": f"""
=== DATABASE RETRIEVAL ===
• **All entities retrieved** - proceed with implementation using stored code
""",
    "single_code_node": f"""
=== DATABASE RETRIEVAL ===
• **Single entity found** - analyze and store implementation details
""",
    "metadata_only": f"""
=== DATABASE RETRIEVAL ===
• **Metadata only** - use GET_CODE_FROM_FILE to get actual code
""",
    "no_results": f"""
=== DATABASE RETRIEVAL ===
• **Nothing found** - check spelling or try semantic search
""",
    "line_specific": f"""
=== DATABASE RETRIEVAL ===
• **Line range specified** - focus on exact code sections needed
""",
}

# Tool-specific instruction templates - Simplified
TOOL_INSTRUCTIONS = {
    "semantic_search": SEMANTIC_SEARCH_GUIDANCE,
    "database": DATABASE_SEARCH_GUIDANCE,
    "git_diff": {
        "basic": """
=== GIT DIFF REQUIREMENTS ===
• **Use proper git diff format** with absolute file paths and accurate line numbers
• **Escape newlines as \\n** in JSON and ensure UTF-8 encoding
• **MANDATORY for all code changes** - never describe without implementing
""",
        "multi_file": """
=== GIT DIFF REQUIREMENTS ===
• **Multiple files** - apply separate git_diff calls for each file
• **Complete all changes** before marking task complete
""",
    },
    "terminal": {
        "basic": """
=== TERMINAL EXECUTION ===
• **Use rg only with exact keywords** from previous semantic_search discoveries
• **Always use context**: `rg -A 5 -B 5 "exact_function_name"`
• **Auto-executes** without confirmation for rg, ls, find commands
""",
        "search_failure": """
=== TERMINAL SEARCH - NO RESULTS ===
• **No matches found** - the search term doesn't exist in codebase
• **Don't guess keywords** - use semantic_search for discovery instead
• **Try different patterns** or switch to database queries if multiple attempts fail
• **Use ls only if needed** to understand project structure before implementing new features
""",
    },
}


def get_tool_specific_instructions(
    tool_type: str,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Get tool-specific instructions based on the tool type and context.

    Args:
        tool_type: The type of tool being used
        context: Additional context that might affect instruction selection

    Returns:
        Tool-specific instruction string
    """
    if tool_type not in TOOL_INSTRUCTIONS:
        return ""

    tool_instructions = TOOL_INSTRUCTIONS[tool_type]

    # Determine which instruction variant to use based on context
    if context:
        # Check for terminal search failure context (highest priority for terminal)
        if tool_type == "terminal" and context.get("search_failure", False):
            return tool_instructions.get(
                "search_failure", tool_instructions.get("basic", "")
            )

        # Check for no results context (highest priority for semantic search)
        if context.get("no_results", False):
            return tool_instructions.get("no_results", tool_instructions.get("basic", ""))

        # Check for chunking context
        if context.get("has_chunking", False):
            return tool_instructions.get("with_chunking", tool_instructions.get("basic", ""))

        # Check for single code node context
        if context.get("single_code_node", False):
            return tool_instructions.get("single_code_node", tool_instructions.get("basic", ""))

        # Check for sequential code delivery context
        if context.get("sequential_code", False):
            # Choose guidance based on whether there are more nodes
            if context.get("has_more_nodes", False):
                return tool_instructions.get("sequential_code", tool_instructions.get("basic", ""))
            else:
                return tool_instructions.get("sequential_code_last", tool_instructions.get("basic", ""))

        # Check for metadata-only context
        if context.get("metadata_only", False):
            return tool_instructions.get("metadata_only", tool_instructions.get("basic", ""))

        # Check for line-specific context
        if context.get("line_specific", False):
            return tool_instructions.get("line_specific", tool_instructions.get("basic", ""))

        # Check for multi-file context
        if context.get("multi_file", False):
            return tool_instructions.get("multi_file", tool_instructions.get("basic", ""))

    # Default to basic instructions
    return tool_instructions.get("basic", "")


def build_dynamic_tool_instructions(
    tools_used: List[str],
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Build dynamic tool instructions for multiple tools.
    
    Args:
        tools_used: List of tool types that will be or have been used
        context: Context information for instruction customization
        
    Returns:
        Combined tool-specific instructions
    """
    instructions = []
    
    for tool_type in tools_used:
        tool_instruction = get_tool_specific_instructions(tool_type, context)
        if tool_instruction:
            instructions.append(tool_instruction)
    
    return "\n".join(instructions)


def determine_tool_context(
    last_action: Optional[Dict[str, Any]] = None,
    last_action_result: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Determine context for tool instructions based on previous actions and results.

    Args:
        last_action: The last action that was executed
        last_action_result: The result of the last action

    Returns:
        Context dictionary for tool instruction selection
    """
    context = {}

    if not last_action_result:
        return context

    # Check for terminal search failures
    if last_action_result.get("tool_type") == "terminal":
        command = last_action_result.get("command", "")
        success = last_action_result.get("success", True)
        output = last_action_result.get("output", "")

        # Detect search commands that failed or returned no results
        search_commands = ["rg", "grep", "find", "ls"]
        is_search_command = any(cmd in command for cmd in search_commands)

        if is_search_command and (
            not success or not output.strip() or "No such file" in output
        ):
            context["search_failure"] = True

    # Check for chunking indicators
    if "chunk" in str(last_action_result).lower():
        context["has_chunking"] = True

    # Check for metadata-only results
    if last_action and last_action.get("parameters", {}).get("code_snippet") is False:
        context["metadata_only"] = True

    # Check for zero results and sequential delivery
    if last_action_result.get("tool_type") == "semantic_search":
        total_nodes = last_action_result.get("total_nodes", 0)
        code_snippet = last_action_result.get("code_snippet", False)
        current_node = last_action_result.get("current_node", 1)
        has_more_nodes = last_action_result.get("has_more_nodes", False)

        if total_nodes == 0:
            context["no_results"] = True
        elif code_snippet and total_nodes == 1:
            # Single node with code content
            context["single_code_node"] = True
        elif code_snippet and total_nodes > 1:
            # Sequential code delivery - multiple nodes with code content
            context["sequential_code"] = True
            # Only show fetch_next_code guidance if there are actually more nodes
            if has_more_nodes or current_node < total_nodes:
                context["has_more_nodes"] = True

    # Check for line-specific queries
    if last_action and (
        "start_line" in last_action.get("parameters", {})
        or "end_line" in last_action.get("parameters", {})
    ):
        context["line_specific"] = True

    # Check for chunking indicators
    result_data = last_action_result.get("data", {})
    if isinstance(result_data, dict):
        # Look for explicit chunk indicators
        if "chunk_info" in result_data or "total_chunks" in result_data:
            context["has_chunking"] = True
        elif any("chunk" in str(key).lower() for key in result_data.keys()):
            context["has_chunking"] = True
        else:
            # Look for line count indicators that suggest chunking (>250 lines)
            for value in result_data.values():
                if "lines" in str(value).lower():
                    # Extract numbers from the value to check if any indicate large files
                    import re
                    numbers = re.findall(r'\d+', str(value))
                    for num_str in numbers:
                        if int(num_str) > CHUNKING_THRESHOLD:  # Use config threshold
                            context["has_chunking"] = True
                            break
                    if context.get("has_chunking"):
                        break

    return context


def build_terminal_search_fallback_content(has_search_failure: bool = False) -> str:
    """
    Build terminal search fallback content for database tools section.

    Args:
        has_search_failure: Whether terminal search commands failed

    Returns:
        Fallback content string or empty string
    """
    if not has_search_failure:
        return ""

    return """
  - GET_NODES_BY_KEYWORD_SEARCH: {{"query_name": "GET_NODES_BY_KEYWORD_SEARCH", "keyword": "search_keyword", "code_snippet": true|false}} - Global search in code content and node names (case-sensitive for code)"""
