"""Dynamic prompt builder for handling sequential delivery instructions."""

from typing import Dict, Any, Optional
from ..delivery_management import delivery_manager


def build_dynamic_action_instructions(
    last_action: Optional[Dict[str, Any]] = None,
    last_action_result: Optional[Dict[str, Any]] = None
) -> Dict[str, str]:
    """
    Build dynamic action instructions based on the current state and tool usage.

    Args:
        last_action: The last action that was executed
        last_action_result: The result of the last action

    Returns:
        Dictionary containing dynamic instructions for different action types
    """
    instructions = {
        "fetch_next_instructions": "",
        "response_format_additions": "",
        "guidelines_additions": "",
        "tool_specific_instructions": ""
    }
    
    # Tool-specific instructions are now handled by the prompt_builder
    # This function focuses only on sequential delivery instructions

    # Build conditional parameter instructions
    instructions["parameter_instructions"] = build_conditional_parameter_instructions(
        last_action_result
    )

    if not last_action or not last_action_result:
        return instructions
    
    # Check if the last action was a database or semantic search that might have more items
    action_type = last_action.get("tool_type", "").lower()
    action_parameters = last_action.get("parameters", {})
    
    if action_type in ["database", "semantic_search"]:
        # Check if there are more items available for this query
        next_item_info = delivery_manager.get_next_item_info(action_type, action_parameters)
        
        if next_item_info.get("has_next", False):
            remaining = next_item_info.get("remaining_items", 0)
            total = next_item_info.get("total_items", 0)
            current = next_item_info.get("current_position", 0)
            
            # Build the fetch next instructions
            action_name = "code" if action_type == "database" else "node"
            
            instructions["fetch_next_instructions"] = f"""
=== SEQUENTIAL DELIVERY AVAILABLE ===
More {action_name}s available: {remaining} remaining out of {total} total (currently at position {current})
To get the next {action_name}, add "fetch_next_{action_name}": true to your action parameters
IMPORTANT: Only use fetch_next_{action_name} if you need more {action_name}s from the same query
"""
            
            instructions["response_format_additions"] = f"""
  // IF you want to get the next {action_name} from the previous query:
  "action": {{
    "tool_type": "{action_type}",
    "parameters": {{
      "fetch_next_{action_name}": true
      // Do NOT include other parameters when using fetch_next_{action_name}
    }}
  }},"""
            
            instructions["guidelines_additions"] = f"""
**Sequential Delivery**: You have {remaining} more {action_name}s available from your previous {action_type} query.
   - Use "fetch_next_{action_name}": true to get the next {action_name}
   - Only use this if you need more {action_name}s from the same query
   - Do NOT use fetch_next_{action_name} if you want to make a different query
"""
        else:
            # No more items available - add guidance to NOT use fetch_next
            action_name = "code" if action_type == "database" else "node"
            instructions["guidelines_additions"] = f"""
**No More {action_name.title()}s**: All {action_name}s from your previous {action_type} query have been delivered.
   - Do NOT use "fetch_next_{action_name}" as there are no more items
   - Make a new query if you need different {action_name}s
"""
    
    return instructions


def enhance_prompt_with_dynamic_instructions(
    base_prompt: str,
    last_action: Optional[Dict[str, Any]] = None,
    last_action_result: Optional[Dict[str, Any]] = None
) -> str:
    """
    Enhance the base prompt with dynamic instructions based on tool usage and context.

    Args:
        base_prompt: The base prompt template
        last_action: The last action that was executed
        last_action_result: The result of the last action

    Returns:
        Enhanced prompt with dynamic instructions
    """
    dynamic_instructions = build_dynamic_action_instructions(last_action, last_action_result)

    # Find the workflow section to add tool-specific instructions
    if "=== WORKFLOW STRATEGY ===" in base_prompt:
        workflow_section_start = base_prompt.find("=== WORKFLOW STRATEGY ===")
        workflow_section_end = base_prompt.find("=== CONTEXT ===", workflow_section_start)

        if workflow_section_end != -1:
            # Insert dynamic instructions after workflow section
            enhanced_prompt = (
                base_prompt[:workflow_section_end] +
                dynamic_instructions["tool_specific_instructions"] +
                dynamic_instructions["fetch_next_instructions"] +
                "\n" +
                base_prompt[workflow_section_end:]
            )
        else:
            enhanced_prompt = base_prompt
    else:
        enhanced_prompt = base_prompt
    
    # Add dynamic response format additions
    if dynamic_instructions["response_format_additions"]:
        response_format_marker = "// IF you need more information OR have code changes ready:"
        if response_format_marker in enhanced_prompt:
            insertion_point = enhanced_prompt.find(response_format_marker)
            enhanced_prompt = (
                enhanced_prompt[:insertion_point] +
                dynamic_instructions["response_format_additions"] +
                "\n  " +
                enhanced_prompt[insertion_point:]
            )
    
    # Add dynamic guidelines
    if dynamic_instructions["guidelines_additions"]:
        rules_section_marker = "=== RULES & DECISION LOGIC ==="
        if rules_section_marker in enhanced_prompt:
            rules_end = enhanced_prompt.find("=== AVAILABLE TOOLS ===")
            if rules_end != -1:
                enhanced_prompt = (
                    enhanced_prompt[:rules_end] +
                    dynamic_instructions["guidelines_additions"] +
                    "\n\n" +
                    enhanced_prompt[rules_end:]
                )
    
    return enhanced_prompt


def should_add_fetch_next_instruction(
    action_type: str,
    action_parameters: Dict[str, Any]
) -> bool:
    """
    Check if we should add fetch_next instruction for the given action.
    
    Args:
        action_type: Type of action (database, semantic_search)
        action_parameters: Parameters of the action
        
    Returns:
        True if fetch_next instruction should be added
    """
    if action_type not in ["database", "semantic_search"]:
        return False
    
    next_item_info = delivery_manager.get_next_item_info(action_type, action_parameters)
    return next_item_info.get("has_next", False)


def get_fetch_next_status(
    action_type: str,
    action_parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Get detailed status about fetch_next availability.
    
    Args:
        action_type: Type of action (database, semantic_search)
        action_parameters: Parameters of the action
        
    Returns:
        Dictionary with fetch_next status information
    """
    if action_type not in ["database", "semantic_search"]:
        return {
            "available": False,
            "reason": "Action type does not support sequential delivery"
        }
    
    next_item_info = delivery_manager.get_next_item_info(action_type, action_parameters)
    
    if next_item_info.get("has_next", False):
        return {
            "available": True,
            "remaining_items": next_item_info.get("remaining_items", 0),
            "total_items": next_item_info.get("total_items", 0),
            "current_position": next_item_info.get("current_position", 0)
        }
    else:
        return {
            "available": False,
            "reason": "No more items available in delivery queue",
            "total_items": next_item_info.get("total_items", 0),
            "is_complete": next_item_info.get("is_complete", False)
        }


def build_conditional_parameter_instructions(
    last_action_result: Optional[Dict[str, Any]] = None
) -> str:
    """
    Build conditional parameter instructions based on current state.

    Args:
        last_action_result: The result of the last action

    Returns:
        Parameter instructions string or empty if not needed
    """
    if not last_action_result:
        return ""

    # Check if this is semantic search with multiple nodes
    if last_action_result.get("tool_type") == "semantic_search":
        # Check if there are more items available using delivery manager
        from src.services.agent.delivery_management import delivery_manager

        query = last_action_result.get("query", "")
        code_snippet = last_action_result.get("code_snippet", False)

        if code_snippet and query:
            # Create minimal parameters to check delivery status
            check_params = {"query": query}

            delivery_info = delivery_manager.get_next_item_info(
                "semantic_search", check_params
            )

            if delivery_info.get("has_next", False):
                return """
  "parameters": {
    // For semantic_search: Add "fetch_next_code": true to get the next available code chunk/node from previous search results
    // This continues sequential delivery of search results without making a new search query
  }"""

    return ""
