"""
Guidance message builder with switch-case logic for different search scenarios.
Provides context-aware guidance messages based on search results and chunking requirements.
"""

from enum import Enum
from typing import Dict, Any, Optional, List
from .guidance_messages import SEMANTIC_SEARCH_MESSAGES, DATABASE_SEARCH_MESSAGES, SEQUE<PERSON>IAL_NODE_MESSAGES
from .constants import SEARCH_CONFIG


class SearchType(Enum):
    SEMANTIC = "semantic"
    DATABASE = "database"


class GuidanceScenario(Enum):
    # Semantic search batch scenarios (simplified)
    SEMANTIC_FIRST_BATCH = "first_batch"
    SEMANTIC_NEXT_BATCH = "next_batch"
    SEMANTIC_FINAL_BATCH = "final_batch"
    SEMANTIC_SINGLE_BATCH = "single_batch"

    # Database search scenarios (keep existing for database queries)
    MULTIPLE_NODES_METADATA_ONLY = "multiple_nodes_metadata_only"
    SINGLE_NODE_METADATA_ONLY = "single_node_metadata_only"
    MULTIPLE_NODES_WITH_CODE_NO_CHUNKING = "multiple_nodes_with_code_no_chunking"
    MULTIPLE_NODES_WITH_CODE_NO_CHUNKING_LAST_NODE = "multiple_nodes_with_code_no_chunking_last_node"
    SINGLE_NODE_SMALL_CODE = "single_node_small_code"
    SINGLE_NODE_LINE_FILTERED = "single_node_line_filtered"
    MULTIPLE_NODES_WITH_CODE_MIXED_CHUNKING = "multiple_nodes_with_code_mixed_chunking"
    SINGLE_NODE_LARGE_CODE_FIRST_CHUNK = "single_node_large_code_first_chunk"
    SINGLE_NODE_LARGE_CODE_SUBSEQUENT_CHUNK = "single_node_large_code_subsequent_chunk"
    SINGLE_NODE_LARGE_CODE_LAST_CHUNK = "single_node_large_code_last_chunk"
    MULTIPLE_NODES_LARGE_CODE_LAST_CHUNK_MORE_NODES = "multiple_nodes_large_code_last_chunk_more_nodes"
    MULTIPLE_NODES_LARGE_CODE_LAST_CHUNK_LAST_NODE = "multiple_nodes_large_code_last_chunk_last_node"

    # Edge case scenarios
    NO_RESULTS_FOUND = "no_results_found"
    NODE_MISSING_CODE_CONTENT = "node_missing_code_content"


class SequentialNodeScenario(Enum):
    NODE_SMALL_CODE = "node_small_code"
    NODE_SMALL_CODE_LAST_NODE = "node_small_code_last_node"
    NODE_LARGE_CODE_FIRST_CHUNK = "node_large_code_first_chunk"
    NODE_LARGE_CODE_SUBSEQUENT_CHUNK = "node_large_code_subsequent_chunk"
    NODE_LARGE_CODE_LAST_CHUNK = "node_large_code_last_chunk"
    NODE_LARGE_CODE_LAST_CHUNK_LAST_NODE = "node_large_code_last_chunk_last_node"
    NODE_NO_CODE_CONTENT = "node_no_code_content"


def determine_guidance_scenario(
    total_nodes: int,
    include_code: bool,
    code_lines: Optional[int] = None,
    chunk_info: Optional[Dict[str, Any]] = None,
    has_large_files: bool = False,
    current_node: int = 1,
    has_more_nodes: bool = False,
    is_line_filtered: bool = False
) -> GuidanceScenario:
    """
    Determine the appropriate guidance scenario using match-case logic.

    Args:
        total_nodes: Total number of nodes found
        include_code: Whether code content is included
        code_lines: Number of lines in the code (for single node scenarios)
        chunk_info: Chunking information if applicable
        has_large_files: Whether any files in the result set are large (>chunking_threshold lines)
        current_node: Current node being processed (1-based)
        has_more_nodes: Whether there are more nodes available

    Returns:
        GuidanceScenario enum value
    """
    # Match-case logic for different scenarios
    match (include_code, total_nodes):
        case (False, 1):
            # Single node, metadata only
            return GuidanceScenario.SINGLE_NODE_METADATA_ONLY

        case (False, _) if total_nodes > 1:
            # Multiple nodes, metadata only
            return GuidanceScenario.MULTIPLE_NODES_METADATA_ONLY

        case (True, 1):
            # Single node with code
            if is_line_filtered:
                return GuidanceScenario.SINGLE_NODE_LINE_FILTERED
            elif code_lines is None or code_lines <= SEARCH_CONFIG["chunking_threshold"]:
                return GuidanceScenario.SINGLE_NODE_SMALL_CODE
            else:
                # Large file - check if this is first chunk, subsequent, or last chunk
                chunk_num = chunk_info.get("chunk_num", 1) if chunk_info else 1
                total_chunks = chunk_info.get("total_chunks", 1) if chunk_info else 1
                is_last_chunk = chunk_num == total_chunks

                match (chunk_num, is_last_chunk):
                    case (1, False):
                        return GuidanceScenario.SINGLE_NODE_LARGE_CODE_FIRST_CHUNK
                    case (_, True):
                        return GuidanceScenario.SINGLE_NODE_LARGE_CODE_LAST_CHUNK
                    case (_, False):
                        return GuidanceScenario.SINGLE_NODE_LARGE_CODE_SUBSEQUENT_CHUNK

        case (True, _) if total_nodes > 1:
            # Multiple nodes with code - check if this is the last node
            is_last_node = current_node == total_nodes and not has_more_nodes

            match (has_large_files, is_last_node):
                case (True, _):
                    # Has large files - need to check if current node is chunked and if it's last chunk
                    if chunk_info:
                        chunk_num = chunk_info.get("chunk_num", 1)
                        total_chunks = chunk_info.get("total_chunks", 1)
                        is_last_chunk = chunk_num == total_chunks

                        if is_last_chunk and is_last_node:
                            return GuidanceScenario.MULTIPLE_NODES_LARGE_CODE_LAST_CHUNK_LAST_NODE
                        elif is_last_chunk:
                            return GuidanceScenario.MULTIPLE_NODES_LARGE_CODE_LAST_CHUNK_MORE_NODES

                    return GuidanceScenario.MULTIPLE_NODES_WITH_CODE_MIXED_CHUNKING
                case (False, True):
                    return GuidanceScenario.MULTIPLE_NODES_WITH_CODE_NO_CHUNKING_LAST_NODE
                case (False, False):
                    return GuidanceScenario.MULTIPLE_NODES_WITH_CODE_NO_CHUNKING

        case _:
            # Default fallback
            return GuidanceScenario.SINGLE_NODE_SMALL_CODE


def determine_sequential_node_scenario(
    code_lines: int,
    chunk_info: Optional[Dict[str, Any]] = None,
    is_last_node: bool = False
) -> SequentialNodeScenario:
    """
    Determine the appropriate sequential node scenario using match-case logic.

    Args:
        code_lines: Number of lines in the code
        chunk_info: Chunking information if applicable
        is_last_node: Whether this is the last node in the sequence

    Returns:
        SequentialNodeScenario enum value
    """
    match code_lines:
        case 0:
            # No code content available
            return SequentialNodeScenario.NODE_NO_CODE_CONTENT
        case n if n <= SEARCH_CONFIG["chunking_threshold"]:
            return SequentialNodeScenario.NODE_SMALL_CODE_LAST_NODE if is_last_node else SequentialNodeScenario.NODE_SMALL_CODE
        case _:
            # Large file - check if this is first chunk, subsequent, or last chunk
            chunk_num = chunk_info.get("chunk_num", 1) if chunk_info else 1
            total_chunks = chunk_info.get("total_chunks", 1) if chunk_info else 1
            is_last_chunk = chunk_num == total_chunks

            match (chunk_num, is_last_chunk, is_last_node):
                case (1, False, _):
                    return SequentialNodeScenario.NODE_LARGE_CODE_FIRST_CHUNK
                case (_, True, True):
                    return SequentialNodeScenario.NODE_LARGE_CODE_LAST_CHUNK_LAST_NODE
                case (_, True, False):
                    return SequentialNodeScenario.NODE_LARGE_CODE_LAST_CHUNK
                case (_, False, _):
                    return SequentialNodeScenario.NODE_LARGE_CODE_SUBSEQUENT_CHUNK


def build_guidance_message(
    search_type: SearchType,
    scenario: GuidanceScenario,
    **kwargs
) -> str:
    """
    Build guidance message based on search type and scenario using match-case logic.

    Args:
        search_type: Type of search (semantic or database)
        scenario: Guidance scenario
        **kwargs: Additional parameters for message formatting

    Returns:
        Formatted guidance message
    """
    # Select message template based on search type using match-case
    match search_type:
        case SearchType.SEMANTIC:
            message_templates = SEMANTIC_SEARCH_MESSAGES
        case SearchType.DATABASE:
            message_templates = DATABASE_SEARCH_MESSAGES
        case _:
            return ""

    # Get template and format with provided parameters
    template = message_templates.get(scenario.value, "")
    if template:
        try:
            return template.format(**kwargs)
        except KeyError:
            # Handle missing parameters gracefully
            return template

    return ""


def build_sequential_node_message(
    scenario: SequentialNodeScenario,
    **kwargs
) -> str:
    """
    Build sequential node message based on scenario.
    
    Args:
        scenario: Sequential node scenario
        **kwargs: Additional parameters for message formatting
    
    Returns:
        Formatted sequential node message
    """
    template = SEQUENTIAL_NODE_MESSAGES.get(scenario.value, "")
    if template:
        try:
            return template.format(**kwargs)
        except KeyError:
            # Handle missing parameters gracefully
            return template

    return ""


def determine_semantic_batch_scenario(
    total_nodes: int, delivered_count: int, remaining_count: int, batch_number: int = 1
) -> GuidanceScenario:
    """
    Determine the appropriate semantic search batch scenario.

    Args:
        total_nodes: Total number of nodes found
        delivered_count: Number of nodes delivered in this batch
        remaining_count: Number of nodes remaining
        batch_number: Current batch number (1-based)

    Returns:
        Appropriate GuidanceScenario for semantic search batch
    """
    # Single batch scenario (all results fit in one batch)
    if total_nodes <= 7:
        return GuidanceScenario.SEMANTIC_SINGLE_BATCH

    # First batch scenario
    if batch_number == 1:
        return GuidanceScenario.SEMANTIC_FIRST_BATCH

    # Final batch scenario (no more results)
    if remaining_count == 0:
        return GuidanceScenario.SEMANTIC_FINAL_BATCH

    # Next batch scenario (more results available)
    return GuidanceScenario.SEMANTIC_NEXT_BATCH


def analyze_result_set_for_large_files(results: List[Dict[str, Any]]) -> bool:
    """
    Analyze a result set to determine if any files are large (>chunking_threshold lines).

    Args:
        results: List of result dictionaries

    Returns:
        True if any files are large, False otherwise
    """
    for result in results:
        code_content = result.get("code_snippet", "")
        if code_content:
            code_lines = len(code_content.split("\n"))
            if code_lines > SEARCH_CONFIG["chunking_threshold"]:
                return True
    return False
