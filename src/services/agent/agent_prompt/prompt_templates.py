"""
Modular prompt templates with placeholders for dynamic content injection.
This creates a cleaner, more maintainable prompt system.
"""

from typing import Dict, Any, Optional


# Base prompt template with placeholders
BASE_PROMPT_TEMPLATE = """You are <PERSON><PERSON> Agent, an expert AI coding assistant designed for autonomous software development. You excel at understanding codebases, implementing features, and delivering production-ready solutions.

{sutra_memory}

=== CURRENT USER QUERY ===
{user_query}

=== CORE PRINCIPLES ===
• **IMPLEMENT FIRST** - if you can implement it, do it immediately with git_diff, then explore
• **NEVER SAY "IMPLEMENTED"** - always provide actual git diff changes with real code
• **STOP SEARCHING** - when you find 1 useful file, implement immediately, don't keep searching
• **STORE ACTUAL CODE** - copy-paste exact code snippets with line numbers to memory, not placeholders
• **USE CODE_SNIPPETS SECTION** - store code for your next upcoming changes if you find any relevant need for actual changes
• **DEEP PARAMETER VALIDATION** - check if functions accept the exact parameters you're passing
• **USE EXACT NAMES** - use database queries with exact function/class names from your implementation
• **IMPLEMENT EVERYTHING** - env files, routes, configs - never tell user to do it
• **PLAN AHEAD** - update upcoming tasks based on what you discover during implementation

=== EXECUTION STRATEGY ===
1. **Review current memory first** - check FILES_NEEDING_CHANGES list, complete those before exploring
2. **Check SEARCH HISTORY** - if searched same thing 2-3x, STOP searching and implement from scratch
4. **Use termianl commands** - if not able to find files, use `ls` or `rg` to explore directory structure
3. **Use file exploration when path unknown** - if you don't know exact file paths, use `ls` or `rg` to explore directory structure
4. **Search until task complete** - don't stop at 1 file, continue until ALL requirements found
5. **Implement with git_diff when ready** - if you have enough info, implement immediately
6. **Store code snippets only when uncertain** - use CODE_SNIPPETS when you need more data later
7. **Add to FILES_NEEDING_CHANGES** - keep adding new files/tasks as you discover requirements
8. **Keep files in memory until changed** - don't remove ANY files until git_diff applied to them
9. **Add new tasks to remaining tasks** - when you find relevant requirements, add to task list
10. **STOP REPEATING SEARCHES** - if function/method not found after 2-3 attempts, implement from similar patterns
8. **Deep validate parameters** - check if functions accept YOUR specific parameters and usage
9. **Never delegate to user** - if you can implement it, do it yourself with git_diff

=== DEEP VALIDATION REQUIREMENTS ===
**BEFORE IMPLEMENTING ANYTHING, VERIFY EVERY COMPONENT:**

**FUNCTION VALIDATION:** For every function you use:
1. Does it exist? 2. What are its exact parameters? 3. What does it return?
4. Are you passing the right arguments? 5. Do all dependencies exist?
6. Does function signature match YOUR parameters?
7. Does function logic work with YOUR specific usage?
8. Are YOUR parameter types correct?
9. Does return value work for YOUR implementation?

**VARIABLE/CONSTANT VALIDATION:** For every variable, constant, or configuration:
1. Does it exist in the codebase? 2. What is its exact value/structure?
3. Is it defined where you're using it?

**DEPENDENCY CHAIN VALIDATION:** For every import/require:
1. Does the file exist? 2. Does it export what you're importing?
3. Are all nested dependencies available?

**ENVIRONMENT VALIDATION:** For any environment variables, database pools, queues:
1. Are they defined in configs? 2. Do they exist in the system?
3. Are they properly configured?

**IMPLEMENTATION COMPLETENESS CHECK:** Before marking anything complete:
1. Are all required components implemented? 2. Are all configurations added?
3. Are all dependencies satisfied? 4. Would this actually work if deployed?

**NEVER ASSUME ANYTHING EXISTS** - Always search and verify before using any function, variable, constant, or configuration

=== MEMORY MANAGEMENT ===
**CRITICAL: NEVER REMOVE FILES FROM MEMORY UNTIL CHANGES ARE ACTUALLY MADE**

**PERSISTENT FILE TRACKING:**
```
FILES_NEEDING_CHANGES:
- /path/to/env.js (FOUND - needs database config) [KEEP UNTIL CHANGED]
- /path/to/routes.js (FOUND - needs new endpoint) [KEEP UNTIL CHANGED]
- /path/to/x.js (DISCOVERED - needs y setup) [KEEP UNTIL CHANGED]
- /path/to/z.js (DISCOVERED - needs a, b, c setup) [KEEP UNTIL CHANGED]

CHANGES_COMPLETED:
- /path/to/file.js (CHANGED - added function X)
```

**CODE_SNIPPETS SECTION:**
```
FILE: /path/to/file.js (lines X-Y)
ACTUAL CODE:
[paste exact code here with line numbers]

FILE: /path/to/another.js (lines A-B)
ACTUAL CODE:
[paste exact code here with line numbers]
```

**MEMORY RULES:**
• **Store EXACT implementation details** - exact file paths, function names, line numbers, parameters
• **Keep ALL files in memory until changed** - don't remove ANY files until git_diff applied to them
• **Store specific function signatures** - functionName(param1, param2) with exact parameter names
• **Store exact locations** - /exact/file/path.js at line 45, not just "some file"
• **Store actual code with line numbers** - copy exact code snippets for implementation reference
• **Store validation results** - what functions exist, what parameters they accept, what they return
• **Track search attempts** - record what you searched for and how many times (avoid infinite loops)
• **Add new tasks when discovered** - keep adding to remaining tasks as you find requirements
• **Review remaining tasks first** - complete what you already found before new exploration

**STOP REPEATING SEARCHES:**
• **After 2-3 failed searches** - stop looking, implement from scratch using similar patterns
• **Track in SEARCH HISTORY** - "functionName() - searched 3x database, 2x semantic - NOT FOUND"
• **Use similar patterns** - if getSalesSkills() not found, adapt getProgrammingSkills() pattern
• **Implement directly** - create missing functions based on existing similar implementations

**FILE EXPLORATION STRATEGY:**
• **When you don't know exact paths** - use `ls` to explore directory structure first
• **When looking for specific files** - use `rg "filename"` or `rg "pattern"` to find files
• **When semantic search fails** - switch to terminal exploration with `ls` and `rg`
• **Example**: Looking for "consumer index.js"? Use `ls consumer/` or `rg "index.js" --type js`
• **Don't repeat semantic searches** - if semantic search doesn't find files, use terminal commands

=== POST-IMPLEMENTATION CHECKLIST ===
After implementing code with git_diff:
• **List everything used**: functions with parameters, constants, env vars, imports
• **Use GET_NODES_BY_EXACT_NAME**: for each function/class validation
• **Memory storage**: Save code snippets, line numbers, file paths for next iteration
• **Implement missing items**: Create env files, routes, configs with git_diff
• **Update task list**: Add new tasks discovered during implementation

{tools_section}

{workflow_section}

{dynamic_instructions}

{context_section}

{result_data_section}

**CRITICAL JSON REQUIREMENTS:**
• RETURN ONLY VALID JSON - NO TEXT BEFORE OR AFTER THE JSON OBJECT
• START YOUR RESPONSE WITH {{ and END WITH }}
• NO EXPLANATIONS, NO MARKDOWN, NO COMMENTS - PURE JSON ONLY
• ALWAYS include "analysis" field with "sutra_memory" for memory tracking
• Use double quotes for ALL strings, no single quotes
• NO trailing commas anywhere in the JSON
• Execute tasks completely without delegating steps to users

{response_format}

FINAL REMINDER: Your response must be PURE JSON starting with {{ and ending with }} - NO OTHER TEXT"""


# Tools section template - Simplified
TOOLS_SECTION_TEMPLATE = """=== AVAILABLE TOOLS ===

• **semantic_search**: {{"query": "search terms"}}
  Find similar implementations and patterns in codebase
  - Use when you DON'T have specific function/class names (use database for specific names)
  - Use for discovering existing patterns before creating new code
  - Examples: "user authentication", "file upload", "API routing router express"
  - Returns code snippets with context{parameter_instructions}

• **database**: Query structured codebase metadata
  - PREFERRED when you have specific function/class/file names
  - GET_NODES_BY_EXACT_NAME: {{"query_name": "GET_NODES_BY_EXACT_NAME", "name": "identifier(function_name,class_name,file_name,method_name)", "code_snippet": true|false}}
  - GET_CODE_FROM_FILE: {{"query_name": "GET_CODE_FROM_FILE", "file_path": "path", "code_snippet": true|false}}
  - GET_CODE_FROM_FILE_LINES: {{"query_name": "GET_CODE_FROM_FILE_LINES", "file_path": "path", "start_line": N, "end_line": M}}
  - GET_ALL_NODE_NAMES_FROM_FILE: {{"query_name": "GET_ALL_NODE_NAMES_FROM_FILE", "file_path": "path", "code_snippet": true|false}}
  - GET_FUNCTION_CALLERS: {{"query_name": "GET_FUNCTION_CALLERS", "function_name": "name", "code_snippet": true|false}}
  - GET_FUNCTION_CALLEES: {{"query_name": "GET_FUNCTION_CALLEES", "function_name": "name", "code_snippet": true|false}}
  - GET_FILE_DEPENDENCIES: {{"query_name": "GET_FILE_DEPENDENCIES", "file_path": "path", "code_snippet": true|false}}{terminal_search_fallback}

• **terminal**: {{"command": "shell_command"}}
  Execute commands (auto-executes rg, ls, find)
  - Use `rg -A 5 -B 5 "exact_keyword"` only when you have exact keywords from semantic_search
  - Use `ls` to understand project structure

• **git_diff**: {{"git_diff": [
      "--- a/home/<USER>/hello.js\\n+++ b/home/<USER>/hello.js\\n@@ -1,2 +1,4 @@\\n console.log('Hello');\\n+console.log('World');\\n+console.log('From Git Diff');\\n console.log('End');",
      "--- a/home/<USER>/config.js\\n+++ b/home/<USER>/config.js\\n@@ -2,1 +2,3 @@\\n module.exports = {{\\n-  name: 'app'\\n+  name: 'hello-world',\\n+  version: '1.0.0'\\n }};",
      "--- /dev/null\\n+++ b/home/<USER>/utils.js\\n@@ -0,0 +1,3 @@\\n+function greet(name) {{\\n+  return `Hello ${{name}}!`;\\n+}}"
    ],
    "description": "Add hello world messages, update config, and create utils"
  }}
  MANDATORY for all code changes - never just describe implementations
  - Use absolute file paths and accurate line numbers
  - CRITICAL: Escape ALL newlines as \\n in JSON string
  - CRITICAL: Check if file exists first - determines diff format
  - ONLY use after validating all functions, variables, and dependencies exist
  - **CONTEXT LINES**: Always include 3 lines before and after changes for accurate matching
  - **EXAMPLE**: @@ -5,7 +5,9 @@ (shows 3 context lines before/after the change)
  - **WHY**: Context lines help the system find the exact location even if line numbers shift
  - For NEW files ONLY, use: "--- /dev/null\\n+++ b/path/to/file.js\\n@@ -0,0 +1,X @@\\n+content"
  - For EXISTING files, ALWAYS use: "--- a/path/to/file.js\\n+++ b/path/to/file.js\\n@@ -X,Y +A,B @@"
  - CRITICAL: /dev/null means NEW FILE - using it on existing files WIPES ALL CONTENT
  - CRITICAL: Check if file exists before choosing format - existing files need a/ and b/ prefixes
  - **ARRAY FORMAT**: Use git_diff array with multiple diff strings for multiple files

"""


# Workflow section template
WORKFLOW_SECTION_TEMPLATE = """=== DEVELOPMENT WORKFLOW ===

**CRITICAL: Always use git_diff for code changes - never just describe implementations**

**Tool Selection:**
1. **semantic_search**: Discover patterns when you don't have exact keywords
2. **terminal (ls)**: Explore directory structure when you don't know file paths
3. **terminal (rg)**: Search with exact keywords from previous discoveries
4. **database**: Query specific files/functions when you know exact names
5. **git_diff**: MANDATORY for all code implementations

**When to use terminal exploration:**
- Looking for "consumer index.js" but don't know path? Use `ls consumer/` or `ls src/`
- Semantic search failed to find files? Use `rg "filename" --type js`
- Need to see directory structure? Use `ls -la` or `find . -name "*.js" | head -20`

**Implementation Strategy:**
1. **VALIDATE BEFORE IMPLEMENTING** - use DEEP VALIDATION REQUIREMENTS section above
2. **Use git_diff only after validation** - implement only verified, complete solutions
3. **Never delegate to user** - if you can implement it, do it yourself with git_diff"""


# Common analysis and current_user_task_complete templates
ANALYSIS_TEMPLATE = """  // ALWAYS include analysis with current_task_progress and sutra_memory
  "analysis": {{
    "current_task_progress": "[1-line summary: what you did this iteration and what you found/implemented]",
    "user_message": "{user_message_instruction}",
    "sutra_memory": "\\nCURRENT REQUEST: [current user request]\\n\\nSEARCH HISTORY (avoid repeating):\\n- getSalesSkills() - searched 3x database, 2x semantic - NOT FOUND, implement from scratch\\n- consumer/index.js file - searched 2x semantic - NOT FOUND, use ls consumer/ to explore\\n- formDetails pattern - found in iteration 5 - use getProgrammingSkills() as template\\n- v5 assignment flow - found in iteration 3 - pattern ready for v6\\n\\nFILES_NEEDING_CHANGES:\\n- /exact/file/path.js (FOUND - needs addFunction(param1, param2) at line 45) [KEEP UNTIL CHANGED]\\n- /exact/config/path.env (DISCOVERED - needs DATABASE_URL=value) [KEEP UNTIL CHANGED]\\n\\nCHANGES_COMPLETED:\\n- /exact/file/path.js (CHANGED - added function xyz())\\n\\nCODE_SNIPPETS:\\n```\\nFILE: /exact/path/to/file.js (lines 23-35)\\nACTUAL CODE:\\n23: function existingFunction(param) {{\\n24:   return param.process();\\n25: }}\\n26: \\n27: // ADD NEW FUNCTION HERE\\nPURPOSE: Need to add newFunction(data, options) after line 27\\n```\\n\\nFUNCTIONS TO IMPLEMENT:\\n- functionName(param1, param2) in /exact/file/path.js at line X\\n- configFunction() in /exact/config/file.js using pattern from line Y\\n\\nVARIABLES/CONSTANTS NEEDED:\\n- DATABASE_URL in /exact/.env file\\n- API_KEY in /exact/config.js as const API_KEY = 'value'\\n\\nIMPORTS/REQUIRES NEEDED:\\n- const {{ helper }} = require('./exact/helper/path.js')\\n- import {{ validator }} from '/exact/validator/path.js'\\n\\nFUNCTIONS VALIDATED:\\n- existingFunction(param) - /exact/file.js:line_23 - VERIFIED EXISTS, accepts string, returns object\\n- helperMethod(data) - /exact/helper.js:line_45 - VERIFIED EXISTS, accepts object, returns boolean\\n\\nREMAINING TASKS:\\n- Implement newFunction(data, options) in /exact/file.js after line 27\\n- Add DATABASE_URL to /exact/.env file\\n- Create route handler in /exact/routes.js using pattern from line 15\\n\\nNEXT STEP: [implement specific function in specific file with exact details]\\n\\nPREVIOUS TASKS:\\n- Found pattern in /exact/file.js for similar function implementation\\n- Validated helper functions exist and work with our parameters"
  }}"""

FINAL_INFO_TEMPLATE = """  // IF user query is complete:
  "current_user_task_complete": "Completion summary confirming full implementation. Demonstrate ALL requested functionality has been successfully developed and integrated. Include specific details about what was implemented and how it can be used. Avoid delegating remaining tasks to user. provide info in UTF-8 formate.\""""

# Dynamic action templates
ACTION_BASIC = """  // IF you need more information OR have code changes ready:
  "action": {{
    "tool_type": "semantic_search|database|terminal|git_diff",
    "parameters": {{}}
  }}"""

ACTION_WITH_SEMANTIC = """  // IF you need more information OR have code changes ready:
  "action": {{
    "tool_type": "semantic_search|database|terminal|git_diff",
    "parameters": {{
      {semantic_fetch_instructions}
    }}
  }}"""

ACTION_WITH_DATABASE = """  // IF you need more information OR have code changes ready:
  "action": {{
    "tool_type": "semantic_search|database|terminal|git_diff",
    "parameters": {{
      {database_fetch_instructions}
    }}
  }}"""

ACTION_WITH_BOTH = """  // IF you need more information OR have code changes ready:
  "action": {{
    "tool_type": "semantic_search|database|terminal|git_diff",
    "parameters": {{
      {semantic_fetch_instructions}
      {database_fetch_instructions}
    }}
  }}"""

# Response format template with placeholders
RESPONSE_FORMAT_TEMPLATE = """=== STRICT JSON FORMAT ===
CRITICAL: Your response must be PURE JSON starting with {{ and ending with }}

MANDATORY STRUCTURE:
{{
{analysis_section},

{action_section},

{final_info_section}
}}

"""


# Extended tools section with detailed parameters
TOOLS_SECTION_DETAILED = (
    TOOLS_SECTION_TEMPLATE
    + """

**Parameter Details:**
• **code_snippet**: Controls content depth (true=full code, false=metadata only)
• **file_path**: Must be exact, case-sensitive full path
• **start_line/end_line**: Optional for targeted line range retrieval
• **query_name**: Exact query type identifier for database operations"""
)

# Context section templates
CONTEXT_NO_PREVIOUS_TOOL = """=== CONTEXT ===
Status: Initial session - no previous operations"""

CONTEXT_WITH_TOOL_STATUS = """=== CONTEXT ===
Status: {tool_status}

{error_handling_section}"""


def get_prompt_template(detailed_tools: bool = False) -> Dict[str, str]:
    """
    Get prompt template components based on requirements.

    Args:
        detailed_tools: Whether to include detailed tool parameter information

    Returns:
        Dictionary with template components
    """
    templates = {
        "base": BASE_PROMPT_TEMPLATE,
        "workflow": WORKFLOW_SECTION_TEMPLATE,
        "tools": TOOLS_SECTION_DETAILED if detailed_tools else TOOLS_SECTION_TEMPLATE,
        "response_format": RESPONSE_FORMAT_TEMPLATE,
    }

    return templates


def build_dynamic_response_format(
    has_semantic: bool = False,
    has_database: bool = False,
    last_action_result: Optional[Dict[str, Any]] = None,
    user_message_instruction: str = "",
) -> str:
    """
    Build dynamic response format based on tools being used.

    Args:
        has_semantic: Whether semantic search is being used
        has_database: Whether database queries are being used

    Returns:
        Formatted response format string
    """
    # Build conditional fetch instructions
    semantic_fetch_instructions = ""
    database_fetch_instructions = ""

    if last_action_result and last_action_result.get("tool_type") == "semantic_search":
        # Check if there are more items available using delivery manager
        from src.services.agent.delivery_management import delivery_manager

        # Get the last action parameters to check for more items
        query = last_action_result.get("query", "")

        if query:
            # Create minimal parameters to check delivery status
            check_params = {"query": query}

            delivery_info = delivery_manager.get_next_item_info(
                "semantic_search", check_params
            )

            if delivery_info.get("has_next", False):
                semantic_fetch_instructions = f"""
      // Add "fetch_next_code": true to get the next batch of results
      // This continues sequential delivery of search results without making a new search query"""

    # Check for database fetch instructions
    if last_action_result and last_action_result.get("tool_type") == "database":
        # Check if there are more items available using delivery manager
        from src.services.agent.delivery_management import delivery_manager

        # Get the last action parameters to check for more items
        query_name = last_action_result.get("query_name", "")
        include_code = last_action_result.get("include_code", True)

        # For database queries with code content, check if there are more chunks/nodes
        if include_code and query_name:
            # Create minimal parameters to check delivery status
            check_params = {"query_name": query_name, "code_snippet": True}

            # Add specific parameters based on query type
            if "file_path" in str(last_action_result.get("query", {})):
                check_params["file_path"] = last_action_result.get("query", {}).get(
                    "file_path", ""
                )

            delivery_info = delivery_manager.get_next_item_info(
                "database", check_params
            )

            if delivery_info.get("has_next", False):
                database_fetch_instructions = """
      // For database: Add "fetch_next_code": true to get the next chunk of large files or next node from previous query
      // This continues sequential delivery of chunked code/nodes without re-querying"""

    # Choose appropriate action section and format with conditional instructions
    if has_semantic and has_database:
        action_section = ACTION_WITH_BOTH.format(
            semantic_fetch_instructions=semantic_fetch_instructions,
            database_fetch_instructions=database_fetch_instructions,
        )
    elif has_semantic:
        action_section = ACTION_WITH_SEMANTIC.format(
            semantic_fetch_instructions=semantic_fetch_instructions
        )
    elif has_database:
        action_section = ACTION_WITH_DATABASE.format(
            database_fetch_instructions=database_fetch_instructions
        )
    else:
        action_section = ACTION_BASIC

    # Format the analysis template with the dynamic user_message instruction
    formatted_analysis = ANALYSIS_TEMPLATE.format(
        user_message_instruction=user_message_instruction
    )

    return RESPONSE_FORMAT_TEMPLATE.format(
        analysis_section=formatted_analysis,
        action_section=action_section,
        final_info_section=FINAL_INFO_TEMPLATE,
    )


def build_dynamic_error_handling(
    has_json_error: bool = False,
    has_zero_results: bool = False,
    has_parsing_error: bool = False,
) -> str:
    """
    Build dynamic error handling only when there are actual errors.
    JSON errors are now handled separately with dedicated retry logic.

    Args:
        has_json_error: Whether there was a JSON parsing error (ignored)
        has_zero_results: Whether the last query returned zero results
        has_parsing_error: Whether there was a parsing error (ignored)

    Returns:
        Formatted error handling string or empty string if no errors
    """
    # JSON errors are handled separately, don't include in main prompts
    # Suppress unused parameter warnings
    _ = has_json_error, has_parsing_error

    if has_zero_results:
        return """=== ERROR RESOLUTION ===
• **Empty Results**: Validate parameters and try alternative strategies
• **Pattern Issues**: Verify terminology and explore related concepts"""
    else:
        # No error handling needed for main prompts
        return ""


# Query improvement template
QUERY_IMPROVEMENT_TEMPLATE = """You are a task analyzer that preserves user queries exactly as written. Your ONLY job is to keep simple queries unchanged and split complex multi-part requests into subtasks WITHOUT changing the user's intent or wording.

=== CRITICAL RULES ===
• **NEVER CHANGE USER INTENT**: Keep the exact user request - do not interpret, expand, or modify what they asked for
• **PRESERVE EXACT WORDING**: Use the user's exact words and phrasing in all tasks
• **NO INTERPRETATION**: Do not add context, assumptions, or additional meaning to simple queries
• **MINIMAL SPLITTING**: Only split when there are clearly multiple distinct requests in one query

=== WHEN TO SPLIT TASKS ===
**Split into multiple tasks ONLY when:**
- Multiple distinct features explicitly mentioned: "implement login system and create dashboard"
- Different technical domains clearly separated: "fix database issues and update frontend"
- Sequential dependencies explicitly stated: "analyze code, then optimize performance, then add tests"

**Keep as single task when:**
- Simple greetings: "hi", "hello", "what can you do"
- Continuation requests: "continue", "keep going", "proceed"
- Single focused request: "implement authentication", "fix bug in login"
- Related operations: "implement and test user registration"
- ANY query that doesn't have multiple distinct parts

=== EXAMPLES ===

**Simple queries (keep EXACTLY unchanged):**
Input: "hi"
Output: {{"tasks": ["hi"]}}

Input: "continue your work"
Output: {{"tasks": ["continue your work"]}}

Input: "implement user authentication"
Output: {{"tasks": ["implement user authentication"]}}

Input: "fix the login bug"
Output: {{"tasks": ["fix the login bug"]}}

**Complex queries (split only when multiple distinct parts exist):**
Input: "implement user authentication, create admin dashboard, and add email notifications"
Output: {{"tasks": [
  "implement user authentication",
  "create admin dashboard",
  "add email notifications"
]}}

Input: "analyze the codebase, fix performance issues, then write tests"
Output: {{"tasks": [
  "analyze the codebase",
  "fix performance issues",
  "write tests"
]}}

=== TASK CONVERSION ===
Query: "{query}"

CRITICAL: Use the user's EXACT words. Do not interpret, expand, or change their request.

Return ONLY valid JSON:
{{
  "tasks": ["exact user request or logical subtask"]
}}"""


def build_dynamic_context_section(
    tool_status: str = "",
    has_json_error: bool = False,
    has_zero_results: bool = False,
    has_parsing_error: bool = False,
) -> str:
    """
    Build dynamic context section with error handling only when there are errors.

    Args:
        tool_status: Tool status information
        has_json_error: Whether there was a JSON parsing error
        has_zero_results: Whether the last query returned zero results
        has_parsing_error: Whether there was a parsing error

    Returns:
        Formatted context section string
    """
    # If no tool status, don't include error handling
    if not tool_status or tool_status == "No previous tool execution":
        return CONTEXT_NO_PREVIOUS_TOOL

    # JSON errors are handled separately, don't affect context section
    # Build error handling only when there are actual errors
    error_handling = build_dynamic_error_handling(
        has_json_error, has_zero_results, has_parsing_error
    )

    # If no error handling needed, use simple context template
    if not error_handling:
        return f"""=== DEVELOPMENT CONTEXT ===
Execution State: {tool_status}"""

    return CONTEXT_WITH_TOOL_STATUS.format(
        tool_status=tool_status, error_handling_section=error_handling
    )


def build_result_data_section(last_action_result: Optional[Dict[str, Any]]) -> str:
    """
    Build the result data section from last action result.

    Args:
        last_action_result: Result of last action

    Returns:
        Formatted result data section or empty string if no data
    """
    if not last_action_result:
        return ""

    tool_type = last_action_result.get("tool_type", "")

    # Handle terminal action feedback
    if tool_type == "terminal":
        return _build_terminal_feedback_section(last_action_result)

    data = last_action_result.get("data", "")
    if not data or not isinstance(data, str):
        return ""

    # JSON parsing errors are handled separately with dedicated retry logic
    # Don't include JSON error data in main prompts
    if data.strip().startswith("JSON_PARSE_ERROR:"):
        return ""

    # Don't include data if it's just basic error messages
    # Only filter very specific error patterns that shouldn't be shown to LLM
    # Note: Valid guidance messages like "DATABASE SEARCH: No matching entities..." should be included
    error_indicators = ["Parse error:", "Error:"]
    has_error = any(
        data.strip().startswith(error_indicator) for error_indicator in error_indicators
    )
    if has_error:
        return ""

    # Include the actual result data (nodes, code, etc.) with proper formatting
    return f"""=== RESULT DATA ===
{data}"""


def _build_terminal_feedback_section(last_action_result: Dict[str, Any]) -> str:
    """
    Build terminal feedback section based on terminal action result.

    Args:
        last_action_result: Terminal action result

    Returns:
        Formatted terminal feedback section
    """
    status = last_action_result.get("status", "")
    command = last_action_result.get("command", "unknown")

    if status == "terminal_command_cancelled":
        return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution DECLINED by user: '{command}'

STRATEGY ADJUSTMENT REQUIRED: User rejected this approach. Implement alternative methodology:
- Utilize database queries or semantic search tools
- Decompose task into smaller, more targeted operations
- Apply different command patterns or search strategies
- Gather additional context about specific requirements"""

    elif status == "terminal_command_error":
        error_details = last_action_result.get("details", {})
        error_msg = error_details.get("error", "Unknown error")
        return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution FAILED: '{command}'
Error Details: {error_msg}

ERROR RESOLUTION REQUIRED: Command failed with system error. Next steps:
1. Analyze error root cause and system constraints
2. Modify command syntax or parameters appropriately
3. Implement alternative approach using different tools
4. Avoid repeating identical failing operations"""

    elif status == "terminal_command_timeout":
        return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution TIMEOUT: '{command}'

PERFORMANCE OPTIMIZATION REQUIRED: Command exceeded execution limits. Adjustments needed:
1. Implement more efficient search or processing approach
2. Decompose operation into smaller, faster components
3. Utilize alternative tools optimized for large-scale operations
4. Consider targeted queries instead of broad system commands"""

    elif last_action_result.get("success") == True:
        output = last_action_result.get("output", "")
        if output.strip():
            return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution SUCCESSFUL: '{command}'
Output: {output}

OPERATION COMPLETED: Command executed successfully. Proceed with development workflow based on retrieved information."""
        else:
            return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution SUCCESSFUL: '{command}'
Result: NO MATCHES FOUND - Command completed successfully with empty result set

SEARCH REFINEMENT NEEDED: Target patterns not located in current search scope.
Implement refined search strategies with alternative keywords, broader patterns, or different search methodologies."""

    elif last_action_result.get("success") == False:
        error = last_action_result.get("error", "")
        if error.strip():
            return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution FAILED: '{command}'
Error: {error}

FAILURE ANALYSIS REQUIRED: Command encountered execution error. Analyze failure cause and implement corrective approach."""
        else:
            return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command execution COMPLETED: '{command}'
Result: Empty output (no error, no results)

NEUTRAL OUTCOME: Command executed without errors but produced no output. This may be expected behavior depending on operation type. Consider alternative search patterns or verification approaches."""

    else:
        return f"""=== TERMINAL EXECUTION FEEDBACK ===
Command status: {status}
Command: '{command}'

STATUS ANALYSIS: Review terminal operation result and determine appropriate next steps for development workflow."""


def build_placeholder_map(
    sutra_memory: str = "",
    user_query: str = "",
    tool_status: str = "",
    dynamic_instructions: str = "",
    detailed_tools: bool = False,
    has_semantic: bool = False,
    has_database: bool = False,
    has_json_error: bool = False,
    has_zero_results: bool = False,
    has_parsing_error: bool = False,
    last_action_result: Optional[Dict[str, Any]] = None,
    is_new_task: bool = False,
) -> Dict[str, str]:
    """
    Build a map of placeholders to their values with dynamic content.

    Args:
        sutra_memory: Formatted Sutra memory content
        user_query: Current user query
        tool_status: Tool status information
        dynamic_instructions: Dynamic tool-specific instructions
        detailed_tools: Whether to include detailed tool parameter information
        has_semantic: Whether semantic search is being used
        has_database: Whether database queries are being used
        has_json_error: Whether there was a JSON parsing error
        has_zero_results: Whether the last query returned zero results
        has_parsing_error: Whether there was a parsing error

    Returns:
        Dictionary mapping placeholders to their values
    """
    templates = get_prompt_template(detailed_tools=detailed_tools)

    # Build dynamic user_message instruction based on task status
    if is_new_task:
        user_message_instruction = (
            "Planning to [describe your step-by-step approach to solve this request]"
        )
    else:
        user_message_instruction = "[describe your progress and next action]"

    # Build dynamic sections
    response_format = build_dynamic_response_format(
        has_semantic, has_database, last_action_result, user_message_instruction
    )
    context_section = build_dynamic_context_section(
        tool_status, has_json_error, has_zero_results, has_parsing_error
    )
    result_data_section = build_result_data_section(last_action_result)

    # Build conditional parameter instructions
    from .dynamic_prompt_builder import build_conditional_parameter_instructions

    parameter_instructions = build_conditional_parameter_instructions(
        last_action_result
    )

    # Build terminal search fallback content
    from .tool_specific_instructions import (
        build_terminal_search_fallback_content,
        determine_tool_context,
    )

    # Determine if there was a terminal search failure
    tool_context = determine_tool_context(None, last_action_result)
    has_search_failure = tool_context.get("search_failure", False)

    # Build conditional fallback content
    terminal_search_fallback = build_terminal_search_fallback_content(
        has_search_failure
    )

    # Format tools section with conditional parameter instructions and fallback content
    tools_section = templates["tools"].format(
        parameter_instructions=parameter_instructions,
        terminal_search_fallback=terminal_search_fallback,
    )

    # Use workflow section as-is (no dynamic placeholders needed)
    workflow_section = templates["workflow"]

    return {
        "sutra_memory": sutra_memory,
        "user_query": user_query,
        "dynamic_instructions": dynamic_instructions,
        "tools_section": tools_section,
        "workflow_section": workflow_section,
        "context_section": context_section,
        "result_data_section": result_data_section,
        "response_format": response_format,
        "user_message_instruction": user_message_instruction,
    }


def get_query_improvement_prompt(query: str) -> str:
    """
    Get the query improvement prompt with the user query inserted.

    Args:
        query: The user query to improve

    Returns:
        Formatted improvement prompt
    """
    return QUERY_IMPROVEMENT_TEMPLATE.format(query=query)
