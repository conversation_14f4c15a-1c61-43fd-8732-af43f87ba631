"""
Clean, modular prompt builder using templates and placeholders.
This replaces the old monolithic prompt building approach.
"""

from typing import Dict, Any, Optional
from .prompt_templates import (
    BASE_PROMPT_TEMPLATE,
    build_placeholder_map,
)
from .tool_specific_instructions import (
    build_dynamic_tool_instructions,
    determine_tool_context,
    get_centralized_thinking_process,
)
from .dynamic_prompt_builder import build_dynamic_action_instructions


class PromptBuilder:
    """
    Clean prompt builder with modular components and placeholders.
    """

    def __init__(self):
        self.base_template = BASE_PROMPT_TEMPLATE

    def build_prompt(
        self,
        user_query: str,
        sutra_memory: str = "",
        tool_status: str = "",
        last_action: Optional[Dict[str, Any]] = None,
        last_action_result: Optional[Dict[str, Any]] = None,
        detailed_tools: bool = False,
        is_new_task: bool = False,
        task_progress_history: str = "",
    ) -> str:
        """
        Build a complete prompt using templates and dynamic instructions.

        Args:
            user_query: Current user query
            sutra_memory: Current Sutra memory content
            tool_status: Tool status information
            last_action: Last action executed
            last_action_result: Result of last action
            detailed_tools: Whether to include detailed tool parameter information

        Returns:
            Complete formatted prompt
        """
        # JSON errors are now handled separately with dedicated retry logic
        # No need to check for JSON errors in main prompt building

        # Format Sutra memory for prompt inclusion
        formatted_memory = self._format_sutra_memory(
            sutra_memory, task_progress_history
        )

        # Build dynamic instructions (includes sequential delivery if needed)
        dynamic_instructions = self._build_dynamic_instructions(
            last_action, last_action_result
        )

        # Build tool status if not provided
        if not tool_status and last_action_result:
            tool_status = self._build_tool_status(last_action_result)

        # Determine tool usage and error conditions for dynamic sections
        has_semantic = self._has_tool_usage("semantic_search", last_action, last_action_result)
        has_database = self._has_tool_usage("database", last_action, last_action_result)
        has_json_error = self._has_json_error(last_action_result)
        has_zero_results = self._has_zero_results(last_action_result)
        has_parsing_error = self._has_parsing_error(last_action_result)

        # Build placeholder map with dynamic content
        placeholders = build_placeholder_map(
            sutra_memory=formatted_memory,
            user_query=user_query,
            tool_status=tool_status,
            dynamic_instructions=dynamic_instructions,
            detailed_tools=detailed_tools,
            has_semantic=has_semantic,
            has_database=has_database,
            has_json_error=has_json_error,
            has_zero_results=has_zero_results,
            has_parsing_error=has_parsing_error,
            last_action_result=last_action_result,
            is_new_task=is_new_task,
        )

        # Format the base template with placeholders
        return self.base_template.format(**placeholders)

    def _format_sutra_memory(
        self, sutra_memory: str, task_progress_history: str = ""
    ) -> str:
        """
        Format Sutra memory for the prompt with proper instructions.

        Args:
            sutra_memory: Current Sutra memory content
            task_progress_history: Task progress history from previous iterations

        Returns:
            Formatted Sutra memory string with instructions
        """
        # Build task progress section
        progress_section = ""
        if task_progress_history:
            progress_section = f"""=== TASK PROGRESS HISTORY ===
{task_progress_history}

"""

        if not sutra_memory:
            return f"""{progress_section}\n=== SUTRA MEMORY ===
(Empty - this is your first iteration)
"""

        return f"""{progress_section}\n=== SUTRA MEMORY ===
{sutra_memory}
"""

    def _build_dynamic_instructions(
        self,
        last_action: Optional[Dict[str, Any]],
        last_action_result: Optional[Dict[str, Any]]
    ) -> str:
        """Build dynamic instructions based on context."""
        # Always include centralized thinking process for efficient tool selection
        thinking_process = get_centralized_thinking_process()

        if not last_action or not last_action_result:
            return thinking_process

        # Get tool-specific instructions
        context = determine_tool_context(last_action, last_action_result)
        tool_type = last_action.get("tool_type", "")

        tool_instructions = ""
        if tool_type:
            tool_instructions = build_dynamic_tool_instructions([tool_type], context)

        # Get sequential delivery instructions
        action_instructions = build_dynamic_action_instructions(
            last_action, last_action_result
        )

        # Combine instructions with centralized thinking process first
        combined_instructions = [thinking_process]
        if tool_instructions:
            combined_instructions.append(tool_instructions)
        if action_instructions.get("fetch_next_instructions"):
            combined_instructions.append(action_instructions["fetch_next_instructions"])

        return "\n".join(combined_instructions)

    def _has_tool_usage(
        self,
        tool_type: str,
        last_action: Optional[Dict[str, Any]],
        last_action_result: Optional[Dict[str, Any]]
    ) -> bool:
        """Check if a specific tool type is being used."""
        if last_action and last_action.get("tool_type") == tool_type:
            return True
        if last_action_result and last_action_result.get("tool_type") == tool_type:
            return True
        return False

    def _has_json_error(self, last_action_result: Optional[Dict[str, Any]]) -> bool:
        """Check if there was a JSON parsing error."""
        if not last_action_result:
            return False

        # Check for new json_parse_error tool type
        if last_action_result.get("tool_type") == "json_parse_error":
            return True

        # Check for legacy JSON_PARSE_ERROR format in data
        result_data = last_action_result.get("data", {})
        if isinstance(result_data, str) and result_data.startswith("JSON_PARSE_ERROR:"):
            return True

        return False

    def _has_zero_results(self, last_action_result: Optional[Dict[str, Any]]) -> bool:
        """Check if the last query returned zero results."""
        if not last_action_result:
            return False

        # Check for zero results in semantic search
        if last_action_result.get("tool_type") == "semantic_search":
            total_nodes = last_action_result.get("total_nodes", 0)
            return total_nodes == 0

        # Check for zero results in database queries
        if last_action_result.get("tool_type") == "database":
            result = last_action_result.get("result", "")
            return "found: 0" in str(result)

        return False

    def _has_parsing_error(self, last_action_result: Optional[Dict[str, Any]]) -> bool:
        """Check if there was a parsing error."""
        if not last_action_result:
            return False

        data = last_action_result.get("data", "")
        if isinstance(data, str):
            return any(error_indicator in data.lower() for error_indicator in [
                "parse error", "parsing failed", "syntax error", "invalid format"
            ])

        return False

    def _build_tool_status(self, last_action_result: Optional[Dict[str, Any]]) -> str:
        """
        Build tool status string from last action result.

        Args:
            last_action_result: Result of last action

        Returns:
            Formatted tool status string
        """
        if not last_action_result:
            return "No previous tool execution"

        tool_type = last_action_result.get("tool_type", "unknown")

        # Handle different tool types
        match tool_type:
            case "semantic_search":
                return self._format_semantic_search_status(last_action_result)
            case "database":
                return self._format_database_status(last_action_result)
            case "terminal":
                return self._format_terminal_status(last_action_result)
            case "git_diff":
                return self._format_git_diff_status(last_action_result)
            case _:
                # Generic formatting for other tools
                result_data = last_action_result.get("data", "")
                if isinstance(result_data, str) and result_data:
                    return f"{tool_type} executed: {result_data[:200]}{'...' if len(result_data) > 200 else ''}"
                else:
                    return f"{tool_type} executed"

    def _format_semantic_search_status(self, action_result: Dict[str, Any]) -> str:
        """Format semantic search results for tool status."""
        query = action_result.get("query", "unknown")
        total_nodes = action_result.get("total_nodes", 0)
        code_snippet = action_result.get("code_snippet", False)

        # Build clean status summary without guidance
        status_parts = [f"Semantic search executed for query: '{query}'"]
        status_parts.append(f"Found {total_nodes} nodes")

        if code_snippet:
            status_parts.append("(with code content)")
        else:
            status_parts.append("(metadata only)")

        return " | ".join(status_parts)

    def _format_database_status(self, action_result: Dict[str, Any]) -> str:
        """Format database query results for tool status."""
        query_name = action_result.get("query_name", "unknown")
        result = action_result.get("result", "")
        query_params = action_result.get("query", {})

        # Build clean status summary without guidance
        status_parts = [f"Database query executed: {query_name}"]

        # Add result count if available
        if result and "found:" in result:
            status_parts.append(result)

        # Add parameter details for line-filtered queries
        if query_name == "GET_CODE_FROM_FILE_LINES" and query_params:
            file_path = query_params.get("file_path", "")
            start_line = query_params.get("start_line")
            end_line = query_params.get("end_line")

            if file_path:
                # Show just the filename for brevity
                filename = file_path.split("/")[-1] if "/" in file_path else file_path
                status_parts.append(f"file: {filename}")

            if start_line and end_line:
                status_parts.append(f"lines: {start_line}-{end_line}")

        return " | ".join(status_parts)

    def _format_terminal_status(self, action_result: Dict[str, Any]) -> str:
        """Format terminal command results for tool status."""
        command = action_result.get("command", "unknown")
        status = action_result.get("status", "")
        success = action_result.get("success")
        output = action_result.get("output", "")

        # Build clean status summary
        if status == "terminal_command_cancelled":
            return f"Terminal command declined by user: '{command}'"
        elif status == "terminal_command_error":
            return f"Terminal command error: '{command}'"
        elif status == "terminal_command_timeout":
            return f"Terminal command timeout: '{command}'"
        elif success == True:
            if output and output.strip():
                return f"Terminal command executed successfully: '{command}' (found results)"
            else:
                return f"Terminal command executed successfully: '{command}' (no results found)"
        elif success == False:
            return f"Terminal command resulted 0 results: '{command}'"
        else:
            return f"Terminal command executed: '{command}'"

    def _format_git_diff_status(self, action_result: Dict[str, Any]) -> str:
        """Format git diff results for tool status."""
        status = action_result.get("status", "unknown")
        data = action_result.get("data", {})

        # Build clean status summary
        status_parts = ["Git diff executed"]

        if status == "success":
            status_parts.append("Success")
            successful_changes = data.get("successful_changes", [])
            if successful_changes:
                if len(successful_changes) == 1:
                    status_parts.append(f"Modified: {successful_changes[0]}")
                else:
                    status_parts.append(f"Modified {len(successful_changes)} files")
        elif status == "error":
            status_parts.append("Failed")
            failed_changes = data.get("failed_changes", [])
            if failed_changes:
                # Include error details for LLM feedback
                error_details = []
                for failed in failed_changes:
                    file_path = failed.get("file_path", "unknown")
                    error = failed.get("error", "unknown error")
                    error_details.append(f"{file_path}: {error}")
                status_parts.append(f"Errors: {'; '.join(error_details)}")
        else:
            status_parts.append(f"Status: {status}")

        return " | ".join(status_parts)


class PromptConfiguration:
    """
    Configuration class for prompt scenarios.
    """

    @staticmethod
    def get_config_for_context(
        last_action: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Get appropriate configuration based on context.

        Args:
            last_action: Last action executed

        Returns:
            Configuration dictionary
        """
        # Default configuration
        config = {
            "detailed_tools": False
        }

        # Check for complex tool usage - use detailed tools for complex queries
        if last_action and last_action.get("tool_type") in ["database", "semantic_search"]:
            params = last_action.get("parameters", {})
            if any(key in params for key in ["start_line", "end_line", "query_name"]):
                config["detailed_tools"] = True

        return config
