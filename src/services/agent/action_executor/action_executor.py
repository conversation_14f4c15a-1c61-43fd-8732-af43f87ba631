from typing import Iterator, Dict, Any, Optional
from src.embeddings.vector_db import VectorDatabase
from src.graph.sqlite_client import SQLiteConnection
from src.services.agent.agentic_core import AgentAction
from src.services.agent.action_executor import (
    execute_terminal_action,
    execute_database_action,
    execute_semantic_search_action,
    execute_git_diff_action,
    get_node_details,
)
from src.config import config


class ActionExecutor:
    def __init__(
        self,
        db_connection: Optional[SQLiteConnection] = None,
        vector_db: Optional[VectorDatabase] = None,
    ):
        self.db_connection = db_connection or SQLiteConnection()
        self.vector_db = vector_db or VectorDatabase(config.sqlite.embeddings_db)

    def execute_terminal_action(self, action: AgentAction) -> Iterator[Dict[str, Any]]:
        yield from execute_terminal_action(action)

    def execute_database_action(self, action: AgentAction) -> Iterator[Dict[str, Any]]:
        yield from execute_database_action(action, self.db_connection)

    def execute_semantic_search_action(
        self, action: AgentAction
    ) -> Iterator[Dict[str, Any]]:
        yield from execute_semantic_search_action(
            action, self.vector_db, self.db_connection
        )

    def get_node_details(self, node_id: int, project_id: int = None):
        return get_node_details(node_id, project_id, self.db_connection)

    def execute_git_diff_action(self, action: AgentAction) -> Iterator[Dict[str, Any]]:
        yield from execute_git_diff_action(action)
