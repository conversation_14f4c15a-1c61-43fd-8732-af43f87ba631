"""
Common utilities for search processing shared between semantic and database searches.
Provides consistent node processing, chunking, and formatting logic.
"""

import json
from typing import Dict, Any, List

from ..utils import (
    beautify_node_result,
    chunk_large_code_clean,
)
from ...agent_prompt.constants import SEARCH_CONFIG
from ...agent_prompt.guidance_builder import (
    SearchType,
    determine_guidance_scenario,
    build_guidance_message,
    analyze_result_set_for_large_files,
)


def needs_chunking(code_content: str) -> bool:
    """
    Check if code content needs chunking based on common threshold.
    
    Args:
        code_content: The code content to check
        
    Returns:
        bool: True if chunking is needed, False otherwise
    """
    return len(code_content.split("\n")) > SEARCH_CONFIG["chunking_threshold"]


def parse_line_information(lines) -> int:
    """
    Parse line information from node details.
    
    Args:
        lines: Line information in various formats
        
    Returns:
        int: Starting line number (defaults to 1)
    """
    file_start_line = 1
    if lines:
        match lines:
            case str():
                try:
                    lines_parsed = json.loads(lines)
                    if isinstance(lines_parsed, list) and len(lines_parsed) >= 2:
                        file_start_line = lines_parsed[0]
                except Exception:
                    pass
            case list() if len(lines) >= 2:
                file_start_line = lines[0]
    return file_start_line


def process_single_node_with_code(
    node_details: Dict[str, Any], 
    index: int, 
    total_nodes: int,
    search_type: SearchType
) -> List[str]:
    """
    Process a single node with code content, handling chunking if needed.
    
    Args:
        node_details: Node details dictionary
        index: Node index (1-based)
        total_nodes: Total number of nodes
        search_type: Type of search (semantic or database)
        
    Returns:
        List[str]: List of formatted result strings
    """
    results = []
    code_content = node_details.get("code_snippet", "")

    if not code_content:
        beautified_result = beautify_node_result(
            node_details, index, include_code=True, total_nodes=total_nodes
        )
        return [beautified_result]

    # Parse line information
    lines = node_details.get("lines")
    file_start_line = parse_line_information(lines)

    if needs_chunking(code_content):
        # Handle chunking
        chunks = chunk_large_code_clean(
            code_content,
            file_start_line=file_start_line,
            max_lines=SEARCH_CONFIG["chunk_size"],
        )

        for chunk in chunks:
            chunk_info = {
                "chunk_num": chunk["chunk_num"],
                "total_chunks": chunk["total_chunks"],
                "chunk_start_line": chunk["chunk_start_line"],
                "chunk_end_line": chunk["chunk_end_line"],
                "total_lines": chunk["total_lines"],
            }

            chunked_node = node_details.copy()
            chunked_node["code_snippet"] = chunk["content"]

            beautified_result = beautify_node_result(
                chunked_node,
                index,
                include_code=True,
                total_nodes=total_nodes,
                chunk_info=chunk_info,
            )
            results.append(beautified_result)
    else:
        # No chunking needed
        beautified_result = beautify_node_result(
            node_details, index, include_code=True, total_nodes=total_nodes
        )
        results.append(beautified_result)

    return results


def build_batch_guidance_message(
    search_type: SearchType,
    total_nodes: int,
    include_code: bool,
    has_large_files: bool = False,
    current_node: int = 1,
    has_more_nodes: bool = False,
    **kwargs
) -> str:
    """
    Build guidance message for batch processing.

    Args:
        search_type: Type of search (semantic or database)
        total_nodes: Total number of nodes
        include_code: Whether code content is included
        has_large_files: Whether any files are large
        current_node: Current node being processed
        has_more_nodes: Whether there are more nodes available
        **kwargs: Additional parameters for guidance formatting

    Returns:
        str: Formatted guidance message
    """
    # Extract parameters that are valid for determine_guidance_scenario
    scenario_params = {
        "total_nodes": total_nodes,
        "include_code": include_code,
        "has_large_files": has_large_files,
        "current_node": current_node,
        "has_more_nodes": has_more_nodes,
    }

    # Add optional parameters if they exist in kwargs
    if "code_lines" in kwargs:
        scenario_params["code_lines"] = kwargs["code_lines"]
    if "chunk_info" in kwargs:
        scenario_params["chunk_info"] = kwargs["chunk_info"]
    if "is_line_filtered" in kwargs:
        scenario_params["is_line_filtered"] = kwargs["is_line_filtered"]

    guidance_scenario = determine_guidance_scenario(**scenario_params)

    guidance_message = build_guidance_message(
        search_type=search_type,
        scenario=guidance_scenario,
        total_nodes=total_nodes,
        current_node=current_node,
        has_more_nodes=has_more_nodes,
        **kwargs
    )

    return guidance_message + "\n\n" if guidance_message else ""


def should_use_sequential_processing(include_code: bool, total_nodes: int) -> bool:
    """
    Determine if results should be processed sequentially.
    
    Args:
        include_code: Whether code content is included
        total_nodes: Total number of nodes
        
    Returns:
        bool: True if sequential processing should be used
    """
    return include_code and total_nodes > 1


def analyze_search_results_for_large_files(results: List[Dict[str, Any]]) -> bool:
    """
    Analyze search results to determine if any files are large.
    
    Args:
        results: List of search result dictionaries
        
    Returns:
        bool: True if any files are large, False otherwise
    """
    return analyze_result_set_for_large_files(results)
