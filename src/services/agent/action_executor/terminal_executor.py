import subprocess
import re
from typing import Iterator, Dict, Any
from src.services.agent.agentic_core import AgentAction


def _enhance_rg_command(command: str) -> str:
    """
    Enhance rg commands to ensure line numbers are shown.

    When rg is run through subprocess (not connected to a tty),
    line numbers are disabled by default. This function adds
    the --line-number flag if it's not already present.

    Args:
        command: The original command string

    Returns:
        Enhanced command string with line numbers enabled for rg
    """
    # Check if this is an rg command
    if not command.strip().startswith('rg '):
        return command

    # Check if line number flags are already present
    # Look for flags with proper boundaries (space or start/end of string)
    if re.search(r'(^|\s)(-n|--line-number|-N|--no-line-number)(\s|$)', command):
        return command

    # Add --line-number flag after 'rg'
    enhanced_command = re.sub(r'^rg\s+', 'rg --line-number ', command)
    return enhanced_command


def execute_terminal_action(action: AgentAction) -> Iterator[Dict[str, Any]]:
    # Enhance rg commands to ensure line numbers are shown
    command = _enhance_rg_command(action.command)
    yield {
        "type": "terminal_command_confirmation",
        "command": command,
        "description": action.description,
    }

    # Auto-execute common commands without asking for confirmation
    auto_execute_commands = ["rg ", "find ", "grep ", "cat "]
    should_auto_execute = any(
        command.strip().startswith(cmd) for cmd in auto_execute_commands
    )

    if should_auto_execute:
        print(f"🖥️  Executing: `{command}`\n")
        if action.description:
            print(f"   Description: {action.description}")
    else:
        print(f"🖥️  Agent wants to execute: `{command}`\n")
        print(f"   Description: {action.description}")
        confirmation = input("   Execute? (yes/no): ").lower().strip()
        if confirmation != "yes":
            yield {"type": "terminal_command_cancelled", "command": command}
            return
    try:
        process_result = subprocess.run(
            command, shell=True, capture_output=True, text=True, timeout=30
        )

        # If rg command with --line-number fails, try without it as fallback
        if (process_result.returncode != 0 and
            command.strip().startswith('rg --line-number ') and
            not process_result.stdout.strip()):

            # Remove --line-number and try again
            fallback_command = command.replace('rg --line-number ', 'rg ', 1)

            try:
                fallback_result = subprocess.run(
                    fallback_command, shell=True, capture_output=True, text=True, timeout=30
                )

                # If fallback succeeds, use its result but mention the fallback
                if fallback_result.returncode == 0:
                    yield {
                        "type": "terminal_command_executed",
                        "command": command,
                        "success": True,
                        "output": fallback_result.stdout,
                        "error": f"Note: Executed without --line-number flag as fallback. Original error: {process_result.stderr}",
                        "return_code": fallback_result.returncode,
                        "fallback_used": True,
                        "original_command": command,
                        "fallback_command": fallback_command,
                    }
                    return
            except Exception:
                # If fallback also fails, continue with original result
                pass

        # Return original result (either success or failure)
        yield {
            "type": "terminal_command_executed",
            "command": command,
            "success": process_result.returncode == 0,
            "output": process_result.stdout,
            "error": process_result.stderr,
            "return_code": process_result.returncode,
        }
    except subprocess.TimeoutExpired:
        yield {"type": "terminal_command_timeout", "command": command}
    except Exception as e:
        yield {
            "type": "terminal_command_error",
            "command": command,
            "error": str(e),
        }
