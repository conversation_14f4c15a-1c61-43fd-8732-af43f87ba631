"""Advanced file change manager with step-by-step change management and git diff support."""

import json
import time
import uuid
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Iterator
from loguru import logger
from dataclasses import dataclass, asdict
from enum import Enum
from src.services.agent.agentic_core import AgentAction
from ....config import config


class ChangeStatus(str, Enum):
    """Status of a file change."""

    PENDING = "pending"  # Change proposed by LLM, waiting for user action
    ACCEPTED = "accepted"  # Change accepted by user
    REVERTED = "reverted"  # Change reverted by user


@dataclass
class FileChange:
    """Represents a single file change with full tracking."""

    change_id: str
    session_id: str
    query_id: str
    file_path: str
    git_diff: str
    status: ChangeStatus
    timestamp: float
    backup_path: str
    original_content: str
    modified_content: str
    description: str = ""


class GitDiffExecutor:
    """Git diff executor for generating and managing code changes."""

    def __init__(self):
        self.changes_dir = Path(config.storage.file_changes_dir)
        self.changes_dir.mkdir(exist_ok=True)
        self.backups_dir = self.changes_dir / "backups"
        self.backups_dir.mkdir(exist_ok=True)

        # Track pending changes
        self.pending_changes: Dict[str, FileChange] = {}
        self._load_pending_changes()

    def apply_git_diff(
        self,
        file_path: str,
        git_diff: str,
        session_id: str,
        query_id: str,
        description: str = "",
    ) -> Iterator[Dict[str, Any]]:
        """
        Apply git diff changes to a file.
        Yields a message when the diff is applied, then the result dict.

        Args:
            file_path: Path to the file to modify
            git_diff: Git diff format string from LLM
            session_id: Current session ID
            query_id: Current query ID
            description: Optional description of the change

        Returns:
            Iterator of result dictionaries
        """
        try:
            # Patch for LLM-generated paths that look absolute but are missing a leading slash
            if not file_path.startswith("/") and "/" in file_path:
                first_part = file_path.split("/")[0]
                # Check if the first part of the path exists at the root level
                if Path("/" + first_part).exists():
                    file_path = "/" + file_path

            is_creation = f"--- /dev/null" in git_diff
            is_deletion = f"+++ /dev/null" in git_diff
            file_to_check = Path(file_path).expanduser().resolve()

            logger.debug(
                f"Applying git diff to {file_path} (creation: {is_creation}, deletion: {is_deletion})"
            )

            original_content = ""
            if is_deletion or not is_creation:
                if not file_to_check.is_file():
                    yield {
                        "success": False,
                        "error": f"File not found for modification/deletion: {file_to_check}",
                    }
                    return
                original_content = file_to_check.read_text()

            # Handle file deletion
            if is_deletion:
                backup_path = self._create_backup(
                    file_path, str(uuid.uuid4()), original_content
                )
                file_to_check.unlink()
                logger.debug(f"🗑️ Deleted file {file_path}")
                yield {
                    "success": True,
                    "change_id": str(uuid.uuid4()),
                    "file_path": file_path,
                    "status": "deleted",
                    "description": description,
                    "backup_path": backup_path,
                    "analysis_summary": f"Deleted file: {file_path}",
                    "changes_detected": {
                        "lines_added": 0,
                        "lines_removed": len(original_content.splitlines()),
                        "total_changes": len(original_content.splitlines()),
                    },
                    "git_diff": git_diff,
                }
                return

            # For creation, ensure parent directory exists
            if is_creation:
                file_to_check.parent.mkdir(parents=True, exist_ok=True)
                logger.debug(f"Created directory structure for {file_path}")

            # Apply git diff to get modified content
            modified_content = self._apply_git_diff(original_content, git_diff)
            if modified_content is None:
                yield {"success": False, "error": "Failed to apply git diff"}
                return

            logger.debug(
                f"Successfully parsed git diff, content length: {len(modified_content)} characters"
            )

            # Create change ID and backup
            change_id = str(uuid.uuid4())
            backup_path = self._create_backup(file_path, change_id, original_content)

            # Create change object
            change = FileChange(
                change_id=change_id,
                session_id=session_id,
                query_id=query_id,
                file_path=file_path,
                git_diff=git_diff,
                status=ChangeStatus.PENDING,
                timestamp=time.time(),
                backup_path=backup_path,
                original_content=original_content,
                modified_content=modified_content,
                description=description,
            )

            # Apply the change to the file
            file_to_check.write_text(modified_content)
            logger.debug(
                f"Successfully wrote {len(modified_content)} characters to {file_path}"
            )

            # Store pending change
            self.pending_changes[change_id] = change
            self._save_pending_changes()

            # Generate analysis summary
            analysis_summary = self._generate_analysis_summary(change)

            yield {
                "type": "git_diff_applied",
                "change_id": change_id,
                "file_path": file_path,
                "message": f"📝 Applied git diff {change_id} to {file_path}",
            }

            yield {
                "success": True,
                "change_id": change_id,
                "file_path": file_path,
                "status": "applied",
                "description": description,
                "backup_path": backup_path,
                "analysis_summary": analysis_summary,
                "changes_detected": self._count_changes(git_diff),
                "git_diff": git_diff,
            }

        except Exception as e:
            logger.error(f"Failed to make change: {e}")
            yield {"success": False, "error": str(e)}

    def accept_change(self, change_id: str) -> Dict[str, Any]:
        """
        User method: Accept a pending change.

        Args:
            change_id: ID of the change to accept

        Returns:
            Dict with success status
        """
        try:
            if change_id not in self.pending_changes:
                return {"success": False, "error": f"Change {change_id} not found"}

            change = self.pending_changes[change_id]
            if change.status != ChangeStatus.PENDING:
                return {
                    "success": False,
                    "error": f"Change {change_id} is not pending (status: {change.status})",
                }

            # Update status to accepted
            change.status = ChangeStatus.ACCEPTED
            self._save_change_record(change)

            # Remove from pending changes
            del self.pending_changes[change_id]
            self._save_pending_changes()

            logger.debug(f"✅ Accepted change {change_id} for {change.file_path}")

            return {
                "success": True,
                "change_id": change_id,
                "file_path": change.file_path,
                "status": "accepted",
            }

        except Exception as e:
            logger.error(f"Failed to accept change: {e}")
            return {"success": False, "error": str(e)}

    def revert_change(self, change_id: str) -> Dict[str, Any]:
        """
        User method: Revert a change (pending or accepted).

        Args:
            change_id: ID of the change to revert

        Returns:
            Dict with success status
        """
        try:
            change = None

            # Check if it's a pending change
            if change_id in self.pending_changes:
                change = self.pending_changes[change_id]
            else:
                # Look for it in accepted changes
                change = self._load_change_record(change_id)
                if not change:
                    return {"success": False, "error": f"Change {change_id} not found"}

            # Restore from backup
            backup_path = Path(change.backup_path)
            if not backup_path.exists():
                return {
                    "success": False,
                    "error": f"Backup file not found: {change.backup_path}",
                }

            # Restore the file
            file_path = Path(change.file_path)
            shutil.copy2(backup_path, file_path)

            # Update status
            change.status = ChangeStatus.REVERTED
            self._save_change_record(change)

            # Remove from pending if it was pending
            if change_id in self.pending_changes:
                del self.pending_changes[change_id]
                self._save_pending_changes()

            logger.debug(f"🔄 Reverted change {change_id} for {change.file_path}")

            return {
                "success": True,
                "change_id": change_id,
                "file_path": change.file_path,
                "status": "reverted",
            }

        except Exception as e:
            logger.error(f"Failed to revert change: {e}")
            return {"success": False, "error": str(e)}

    def show_diff(self, change_id: str) -> Dict[str, Any]:
        """
        Show the diff for a change.

        Args:
            change_id: ID of the change

        Returns:
            Dict with diff information
        """
        try:
            change = None

            # Check pending changes first
            if change_id in self.pending_changes:
                change = self.pending_changes[change_id]
            else:
                # Look in change records
                change = self._load_change_record(change_id)
                if not change:
                    return {"success": False, "error": f"Change {change_id} not found"}

            return {
                "success": True,
                "change_id": change_id,
                "file_path": change.file_path,
                "git_diff": change.git_diff,
                "status": change.status,
                "description": change.description,
                "timestamp": change.timestamp,
            }

        except Exception as e:
            logger.error(f"Failed to show diff: {e}")
            return {"success": False, "error": str(e)}

    def list_pending_changes(
        self, session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List all pending changes, optionally filtered by session.

        Args:
            session_id: Optional session ID to filter by

        Returns:
            List of pending changes
        """
        try:
            changes = []
            for change in self.pending_changes.values():
                if session_id is None or change.session_id == session_id:
                    changes.append(
                        {
                            "change_id": change.change_id,
                            "file_path": change.file_path,
                            "description": change.description,
                            "timestamp": change.timestamp,
                            "session_id": change.session_id,
                            "query_id": change.query_id,
                        }
                    )

            return sorted(changes, key=lambda x: x["timestamp"], reverse=True)

        except Exception as e:
            logger.error(f"Failed to list pending changes: {e}")
            return []

    # Helper methods
    def _generate_diff(
        self, original_content: str, modified_content: str, file_path: str
    ) -> str:
        """Generate git diff between original and modified content."""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = Path(temp_dir)

                # Write original and modified content to temp files
                original_file = temp_dir_path / "original.txt"
                modified_file = temp_dir_path / "modified.txt"

                original_file.write_text(original_content)
                modified_file.write_text(modified_content)

                # Generate diff using git diff
                result = subprocess.run(
                    [
                        "git",
                        "diff",
                        "--no-index",
                        str(original_file),
                        str(modified_file),
                    ],
                    capture_output=True,
                    text=True,
                    cwd=temp_dir,
                )

                if result.stdout:
                    # Replace temp file paths with actual file path
                    diff_lines = result.stdout.splitlines()
                    for i, line in enumerate(diff_lines):
                        if line.startswith("--- "):
                            diff_lines[i] = f"--- a/{file_path}"
                        elif line.startswith("+++ "):
                            diff_lines[i] = f"+++ b/{file_path}"

                    return "\n".join(diff_lines)

                return ""

        except Exception as e:
            logger.error(f"Failed to generate diff: {e}")
            return ""

    def _generate_analysis_summary(self, change: FileChange) -> str:
        """Generate comprehensive analysis summary for conversation history and future LLM calls."""
        try:
            lines_added = change.git_diff.count("\n+") - change.git_diff.count("\n+++")
            lines_removed = change.git_diff.count("\n-") - change.git_diff.count(
                "\n---"
            )

            # Extract function/class names from the diff for context
            modified_functions = self._extract_modified_functions(change.git_diff)

            summary_parts = [
                f"Applied git diff to {change.file_path}",
                f"Changes: +{lines_added} lines added, -{lines_removed} lines removed",
                f"Description: {change.description}" if change.description else "",
                (
                    f"Modified functions/classes: {', '.join(modified_functions)}"
                    if modified_functions
                    else ""
                ),
                f"Change ID: {change.change_id} (for rollback if needed)",
                "File has been successfully modified and backup created",
                "Changes are now active in the codebase",
                "Future iterations can reference these modifications",
                "If further changes needed to this file, use current modified state as baseline",
            ]

            return " | ".join(filter(None, summary_parts))

        except Exception as e:
            logger.error(f"Failed to generate analysis summary: {e}")
            return f"Git diff applied to {change.file_path} - {change.description} - Change ID: {change.change_id}"

    def _extract_modified_functions(self, git_diff: str) -> List[str]:
        """Extract function/class names that were modified from git diff."""
        try:
            functions = []
            lines = git_diff.splitlines()

            for line in lines:
                # Look for function definitions
                if line.startswith("+") or line.startswith("-"):
                    line_content = line[1:].strip()
                    if line_content.startswith("def "):
                        # Python function
                        func_name = (
                            line_content.split("(")[0].replace("def ", "").strip()
                        )
                        if func_name and func_name not in functions:
                            functions.append(func_name)
                    elif line_content.startswith("class "):
                        # Python class
                        class_name = (
                            line_content.split("(")[0]
                            .split(":")[0]
                            .replace("class ", "")
                            .strip()
                        )
                        if class_name and class_name not in functions:
                            functions.append(class_name)
                    elif "function " in line_content or "const " in line_content:
                        # JavaScript function
                        if "function " in line_content:
                            parts = line_content.split("function ")
                            if len(parts) > 1:
                                func_name = parts[1].split("(")[0].strip()
                                if func_name and func_name not in functions:
                                    functions.append(func_name)

            return functions[:5]  # Limit to first 5 functions to avoid too long summary

        except Exception as e:
            logger.error(f"Failed to extract modified functions: {e}")
            return []

    def _count_changes(self, git_diff: str) -> Dict[str, int]:
        """Count the number of changes in a git diff."""
        try:
            lines = git_diff.splitlines()
            added = 0
            removed = 0

            for line in lines:
                if line.startswith("+") and not line.startswith("+++"):
                    added += 1
                elif line.startswith("-") and not line.startswith("---"):
                    removed += 1

            return {
                "lines_added": added,
                "lines_removed": removed,
                "total_changes": added + removed,
            }

        except Exception as e:
            logger.error(f"Failed to count changes: {e}")
            return {"lines_added": 0, "lines_removed": 0, "total_changes": 0}

    def _extract_filepath_from_diff(self, git_diff: str) -> Optional[str]:
        """Extracts the file path from the git diff string."""
        try:
            # First, try to extract from diff --git header (for malformed diffs)
            for line in git_diff.splitlines():
                if line.startswith("diff --git"):
                    # Extract from: diff --git a/path/to/file b/path/to/file
                    parts = line.split()
                    if len(parts) >= 4:
                        # Use the b/ path (new file path)
                        b_path = parts[3]
                        if b_path.startswith("b/"):
                            path = b_path[2:]
                            # Ensure the path has a leading slash if it's an absolute path
                            if path.startswith("home/") and not path.startswith("/"):
                                path = "/" + path
                            return path
                        elif b_path.startswith("/"):
                            return b_path
                        else:
                            # Relative path
                            return b_path

            # Try to get the new path first - handle multiple formats
            for line in git_diff.splitlines():
                if line.startswith("+++"):
                    # Handle different git diff formats:
                    # Standard: "+++ b/path" or "+++ a/path"
                    # Absolute: "+++ /absolute/path"
                    # Relative: "+++ relative/path"
                    if line.startswith("+++ b/") or line.startswith("+++ a/"):
                        path = line[6:].strip()
                    elif line.startswith("+++ /"):
                        path = line[4:].strip()
                    elif line.startswith("+++ "):
                        # Handle relative paths without a/ or b/ prefix
                        path = line[4:].strip()
                    else:
                        continue

                    if path != "/dev/null":
                        # Ensure the path has a leading slash if it's an absolute path
                        if path.startswith("home/") and not path.startswith("/"):
                            path = "/" + path
                        return path

            # If no new path (e.g., file deletion), get the old path
            for line in git_diff.splitlines():
                if line.startswith("---"):
                    # Handle different git diff formats:
                    # Standard: "--- a/path" or "--- b/path"
                    # Absolute: "--- /absolute/path"
                    # Relative: "--- relative/path"
                    if line.startswith("--- a/") or line.startswith("--- b/"):
                        path = line[6:].strip()
                    elif line.startswith("--- /"):
                        path = line[4:].strip()
                    elif line.startswith("--- "):
                        # Handle relative paths without a/ or b/ prefix
                        path = line[4:].strip()
                    else:
                        continue

                    if path != "/dev/null":
                        # Ensure the path has a leading slash if it's an absolute path
                        if path.startswith("home/") and not path.startswith("/"):
                            path = "/" + path
                        return path

            return None
        except Exception as e:
            logger.error(f"Failed to extract filepath from diff: {e}")
            return None

    def _split_unified_diff_by_files(
        self, full_diff: str, file_paths: List[str]
    ) -> List[str]:
        """Split a unified diff containing multiple files into separate diffs."""
        try:
            lines = full_diff.splitlines()
            diff_chunks = []
            current_chunk = []
            current_file = None

            i = 0
            while i < len(lines):
                line = lines[i]

                # Check if this line indicates a new file
                if line.startswith("--- "):
                    # If we have a current chunk, save it
                    if current_chunk and current_file:
                        diff_chunks.append("\n".join(current_chunk))

                    # Start new chunk
                    current_chunk = [line]

                    # Extract file path from --- line
                    if line.startswith("--- a/"):
                        current_file = line[6:].strip()
                    elif line.startswith("--- /"):
                        current_file = line[4:].strip()
                    elif line.startswith("--- "):
                        current_file = line[4:].strip()

                    # Look for the corresponding +++ line
                    if i + 1 < len(lines) and lines[i + 1].startswith("+++"):
                        current_chunk.append(lines[i + 1])
                        i += 1

                elif current_chunk:
                    # Add line to current chunk
                    current_chunk.append(line)

                i += 1

            # Add the last chunk
            if current_chunk and current_file:
                diff_chunks.append("\n".join(current_chunk))

            logger.debug(
                f"Split unified diff into {len(diff_chunks)} chunks for files: {file_paths}"
            )
            return diff_chunks

        except Exception as e:
            logger.error(f"Failed to split unified diff: {e}")
            return []

    def _validate_git_diff_format(self, git_diff: str) -> tuple[bool, str]:
        """Validate that the git diff has proper format."""
        lines = git_diff.splitlines()

        # Check for file headers
        has_old_header = any(line.startswith("--- ") for line in lines)
        has_new_header = any(line.startswith("+++ ") for line in lines)
        has_hunk_header = any(line.startswith("@@") for line in lines)

        if not has_old_header:
            return False, "Missing '--- a/filename' header"
        if not has_new_header:
            return False, "Missing '+++ b/filename' header"
        if not has_hunk_header:
            return (
                False,
                "Missing '@@ -old_start,old_count +new_start,new_count @@' hunk header",
            )

        return True, "Valid git diff format"

    def _apply_git_diff(self, original_content: str, git_diff: str) -> Optional[str]:
        """Apply a git diff to content and return the modified content."""
        try:
            # For malformed diffs, go directly to manual parsing
            is_valid, _ = self._validate_git_diff_format(git_diff)
            if not is_valid:
                return self._parse_diff_manually(original_content, git_diff)

            # Check if this is a new file creation
            is_new_file = "--- /dev/null" in git_diff

            # For new files, use manual parsing directly since git apply doesn't work well
            if is_new_file:
                return self._parse_diff_manually(original_content, git_diff)

            # Create temporary files for git apply
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = Path(temp_dir)

                # Write original content to temp file
                original_file = temp_dir_path / "original.txt"
                original_file.write_text(original_content)

                # Write diff to temp file
                diff_file = temp_dir_path / "changes.diff"
                diff_file.write_text(git_diff)

                # Apply the diff using git apply
                result = subprocess.run(
                    ["git", "apply", "--no-index", str(diff_file)],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                )

                if result.returncode == 0:
                    # Read the modified content
                    return original_file.read_text()
                else:
                    # Git apply failed, try manual parsing
                    return self._parse_diff_manually(original_content, git_diff)

        except Exception as e:
            logger.error("git diff applied failed")
            return None

    def _parse_diff_manually(
        self, original_content: str, git_diff: str
    ) -> Optional[str]:
        """Manually parse and apply a simple git diff."""
        try:
            lines = original_content.splitlines(keepends=True)
            diff_lines = git_diff.splitlines()

            # Check if this is a new file creation
            is_new_file = False
            for line in diff_lines:
                if line.startswith("--- /dev/null"):
                    is_new_file = True
                    break

            if is_new_file:
                # For new files, extract all lines that start with '+'
                new_content_lines = []
                for line in diff_lines:
                    if line.startswith("+") and not line.startswith("+++"):
                        # Remove the '+' prefix and add to new content
                        content_line = line[1:]
                        if not content_line.endswith("\n"):
                            content_line += "\n"
                        new_content_lines.append(content_line)

                result = "".join(new_content_lines)
                return result

            # Check if we have any hunk headers at all
            has_hunk_headers = any(line.startswith("@@") for line in diff_lines)

            if not has_hunk_headers:
                # Try to parse as a simple diff without proper headers
                return self._parse_simple_diff(original_content, diff_lines)

            # Advanced diff parser with context-aware line matching
            i = 0
            while i < len(diff_lines):
                line = diff_lines[i]

                if line.startswith("@@"):
                    # Parse hunk header: @@ -start,count +start,count @@
                    parts = line.split()
                    if len(parts) >= 3:
                        old_range = parts[1][1:]  # Remove '-'
                        old_start = (
                            int(old_range.split(",")[0]) - 1
                        )  # Convert to 0-based

                        # Collect all hunk lines first
                        i += 1
                        hunk_lines = []
                        while i < len(diff_lines) and not diff_lines[i].startswith(
                            "@@"
                        ):
                            hunk_lines.append(diff_lines[i])
                            i += 1

                        # Apply the hunk using context-aware matching
                        self._apply_hunk_with_context(lines, hunk_lines, old_start)
                        continue

                i += 1

            result = "".join(lines)
            return result

        except Exception as e:
            logger.error("git diff applied failed")
            return None

    def _parse_simple_diff(
        self, original_content: str, diff_lines: list
    ) -> Optional[str]:
        """Parse a simple diff without proper headers, looking for + and - lines."""
        try:
            lines = original_content.splitlines(keepends=True)

            # Look for lines that start with + or - and try to apply them
            additions = []
            deletions = []

            for line in diff_lines:
                if line.startswith("+") and not line.startswith("+++"):
                    # Addition
                    content = line[1:]
                    if not content.endswith("\n"):
                        content += "\n"
                    additions.append(content)
                elif line.startswith("-") and not line.startswith("---"):
                    # Deletion
                    content = line[1:].rstrip("\n\r")
                    deletions.append(content)

            # If we have additions but no deletions, append to the end
            if additions and not deletions:
                lines.extend(additions)
                return "".join(lines)

            # If we have deletions, try to find and remove them
            if deletions:
                for deletion in deletions:
                    for i, line in enumerate(lines):
                        if line.rstrip("\n\r") == deletion:
                            lines.pop(i)
                            break

            # Add any additions to the end
            if additions:
                lines.extend(additions)

            return "".join(lines)

        except Exception as e:
            logger.error("git diff applied failed")
            return None

    def _apply_hunk_with_context(
        self, lines: list, hunk_lines: list, expected_start: int
    ) -> None:
        """Apply a hunk using context lines to find the correct position."""
        try:
            # Extract context lines and changes from the hunk
            context_before = []
            context_after = []
            changes = []

            # Parse hunk to separate context and changes
            for line in hunk_lines:
                if line.startswith(" "):
                    # Context line
                    if not changes:
                        context_before.append(line[1:].rstrip("\n\r"))
                    else:
                        context_after.append(line[1:].rstrip("\n\r"))
                elif line.startswith("-") or line.startswith("+"):
                    changes.append(line)

            # Find the actual position using context
            actual_start = self._find_position_with_context(
                lines, context_before, expected_start
            )

            if actual_start == -1:
                logger.warning(
                    f"Could not find position for hunk, using expected position {expected_start}"
                )
                actual_start = expected_start

            # Apply changes at the found position
            current_pos = actual_start + len(context_before)

            # Process changes in order
            for change_line in changes:
                if change_line.startswith("-"):
                    # Remove line
                    content = change_line[1:].rstrip("\n\r")
                    if current_pos < len(lines):
                        original_line = lines[current_pos].rstrip("\n\r")
                        if original_line == content:
                            lines.pop(current_pos)
                        else:
                            # Try to find the line nearby
                            found_pos = self._find_line_nearby(
                                lines, content, current_pos, 3
                            )
                            if found_pos != -1:
                                lines.pop(found_pos)
                                # Adjust current position if we removed a line before it
                                if found_pos < current_pos:
                                    current_pos -= 1
                            else:
                                logger.warning(
                                    f"Could not find line to remove: '{content}'"
                                )
                    else:
                        logger.warning(
                            f"Position {current_pos} out of bounds for removal"
                        )

                elif change_line.startswith("+"):
                    # Add line
                    content = change_line[1:]
                    if not content.endswith("\n"):
                        content += "\n"
                    lines.insert(current_pos, content)
                    current_pos += 1

        except Exception as e:
            logger.error(f"Failed to apply hunk with context: {e}")

    def _find_position_with_context(
        self, lines: list, context_before: list, expected_start: int
    ) -> int:
        """Find the actual position using context lines."""
        if not context_before:
            return expected_start

        # Try exact position first
        if self._context_matches(lines, context_before, expected_start):
            return expected_start

        # Search nearby positions
        for offset in range(-5, 6):  # Check 5 lines before and after
            pos = expected_start + offset
            if pos >= 0 and self._context_matches(lines, context_before, pos):
                return pos

        return -1

    def _context_matches(self, lines: list, context: list, start_pos: int) -> bool:
        """Check if context lines match at the given position."""
        if start_pos < 0 or start_pos + len(context) > len(lines):
            return False

        for i, context_line in enumerate(context):
            file_line = lines[start_pos + i].rstrip("\n\r")
            if file_line != context_line:
                return False
        return True

    def _find_line_nearby(
        self, lines: list, target: str, center: int, radius: int
    ) -> int:
        """Find a line near the given position."""
        for offset in range(-radius, radius + 1):
            pos = center + offset
            if 0 <= pos < len(lines):
                if lines[pos].rstrip("\n\r") == target:
                    return pos
        return -1

    def _create_backup(self, file_path: str, change_id: str, content: str) -> str:
        """Create a backup of the file content."""
        backup_filename = f"{change_id}_{Path(file_path).name}.backup"
        backup_path = self.backups_dir / backup_filename

        backup_path.write_text(content)
        return str(backup_path)

    def _save_pending_changes(self) -> None:
        """Save pending changes to disk."""
        try:
            pending_file = self.changes_dir / "pending_changes.json"
            pending_data = {}

            for change_id, change in self.pending_changes.items():
                pending_data[change_id] = asdict(change)

            with open(pending_file, "w") as f:
                json.dump(pending_data, f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save pending changes: {e}")

    def _load_pending_changes(self) -> None:
        """Load pending changes from disk."""
        try:
            pending_file = self.changes_dir / "pending_changes.json"
            if pending_file.exists():
                with open(pending_file, "r") as f:
                    pending_data = json.load(f)

                for change_id, change_dict in pending_data.items():
                    change_dict["status"] = ChangeStatus(change_dict["status"])
                    self.pending_changes[change_id] = FileChange(**change_dict)

        except Exception as e:
            logger.error(f"Failed to load pending changes: {e}")

    def _save_change_record(self, change: FileChange) -> None:
        """Save a change record to disk."""
        try:
            record_file = self.changes_dir / f"{change.change_id}.json"
            with open(record_file, "w") as f:
                json.dump(asdict(change), f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save change record: {e}")

    def _load_change_record(self, change_id: str) -> Optional[FileChange]:
        """Load a change record from disk."""
        try:
            record_file = self.changes_dir / f"{change_id}.json"
            if record_file.exists():
                with open(record_file, "r") as f:
                    change_dict = json.load(f)

                change_dict["status"] = ChangeStatus(change_dict["status"])
                return FileChange(**change_dict)

            return None

        except Exception as e:
            logger.error(f"Failed to load change record: {e}")
            return None


def execute_git_diff_action(action: AgentAction) -> Iterator[Dict[str, Any]]:
    """
    Execute git diff action to apply code changes from LLM-generated git diff.
    This function can handle multi-file git diffs, creations, and deletions.

    Args:
        action: AgentAction containing git diff parameters
    Yields:
        A single dictionary containing the consolidated results of all git diff operations.
    """
    try:
        executor = GitDiffExecutor()
        parameters = action.parameters or {}
        git_diff_param = parameters.get("git_diff")
        description = parameters.get("description", "Applying multi-file changes.")
        session_id = parameters.get("session_id", "default")
        query_id = parameters.get("query_id", "default")

        if not git_diff_param:
            yield {
                "tool_type": "git_diff",
                "status": "error",
                "data": {"error": "git_diff parameter is required"},
            }
            return

        # Handle different git diff formats
        diff_chunks = []

        # Check if git_diff is an array (new format)
        if isinstance(git_diff_param, list):
            logger.info(f"Processing git_diff array with {len(git_diff_param)} diffs")
            diff_chunks = git_diff_param
        elif isinstance(git_diff_param, str):
            # Legacy string format - handle as before
            full_git_diff = git_diff_param

            # Check if it starts with "diff --git" (multi-file format)
            if full_git_diff.startswith("diff --git"):
                # Split the full diff into individual diffs for each file
                chunks = full_git_diff.split("diff --git ")
                diff_chunks = [
                    "diff --git " + chunk for chunk in chunks if chunk.strip()
                ]
            else:
                # Single file diff or unified diff format
                # Check if it contains multiple file paths
                lines = full_git_diff.splitlines()
                file_paths = []

                for line in lines:
                    if line.startswith("+++ b/"):
                        path = line[6:].strip()
                        if path != "/dev/null" and path not in file_paths:
                            file_paths.append(path)
                    elif line.startswith("--- a/"):
                        path = line[6:].strip()
                        if path != "/dev/null" and path not in file_paths:
                            file_paths.append(path)

                if len(file_paths) > 1:
                    # Multiple files detected, try to split by file boundaries
                    logger.info(
                        f"Multiple files detected ({len(file_paths)} files), attempting to split diff"
                    )
                    diff_chunks = executor._split_unified_diff_by_files(
                        full_git_diff, file_paths
                    )
                    if not diff_chunks:
                        logger.warning(
                            "Failed to split multi-file diff, treating as single diff"
                        )
                        diff_chunks = [full_git_diff]
                    else:
                        logger.info(
                            f"Successfully split diff into {len(diff_chunks)} chunks"
                        )
                else:
                    # Single file diff
                    diff_chunks = [full_git_diff]
        else:
            yield {
                "tool_type": "git_diff",
                "status": "error",
                "data": {"error": "git_diff must be a string or array of strings"},
            }
            return

        logger.debug(f"Processing {len(diff_chunks)} git diff chunk(s)")

        all_results = []
        for i, git_diff_chunk in enumerate(diff_chunks):
            logger.debug(f"Processing diff chunk {i+1}/{len(diff_chunks)}")

            file_path = executor._extract_filepath_from_diff(git_diff_chunk)
            if not file_path:
                error_msg = f"Could not extract file path from chunk {i+1}"
                logger.warning(f"{error_msg}, skipping: {git_diff_chunk[:200]}")
                # Add failed change to results for LLM feedback
                all_results.append(
                    {"success": False, "file_path": f"chunk_{i+1}", "error": error_msg}
                )
                continue

            logger.debug(f"Extracted file path: {file_path}")

            for result in executor.apply_git_diff(
                file_path=file_path,
                git_diff=git_diff_chunk,
                session_id=session_id,
                query_id=query_id,
                description=description,
            ):
                yield result
                # Collect both successful and failed results for summary
                if result.get("success") is not None:  # Only collect final result dicts
                    all_results.append(result)

        # Consolidate results into a single summary
        successful_changes = [res for res in all_results if res.get("success")]
        failed_changes = [res for res in all_results if not res.get("success")]
        status = "error" if failed_changes else "success"

        summary_message = (
            f"Successfully applied changes to {len(successful_changes)} file(s)."
        )
        if failed_changes:
            summary_message += (
                f" Failed to apply changes to {len(failed_changes)} file(s)."
            )

        yield {
            "tool_type": "git_diff",
            "status": status,
            "data": {
                "summary": summary_message,
                "successful_changes": [
                    res.get("file_path") for res in successful_changes
                ],
                "failed_changes": [
                    {"file_path": res.get("file_path"), "error": res.get("error")}
                    for res in failed_changes
                ],
            },
        }

    except Exception as e:
        logger.error(f"Git diff action execution failed: {e}")
        yield {
            "tool_type": "git_diff",
            "status": "error",
            "data": {"error": f"Git diff execution failed: {str(e)}"},
        }
