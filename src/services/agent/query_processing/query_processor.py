"""Query processing and improvement for agent service."""

import json
import time
from typing import Dict, Any, Iterator
from loguru import logger

from ..agent_prompt import get_query_improvement_prompt


class QueryProcessor:
    """Handles query improvement and processing."""

    def __init__(self, llm_client, response_processor):
        self.llm_client = llm_client
        self.response_processor = response_processor

    def improve_query(self, problem_query: str) -> Iterator[Dict[str, Any]]:
        """
        Improve the user query and yield updates.

        Args:
            problem_query: The original query from user

        Yields:
            Updates about query improvement process
        """
        yield {
            "type": "query_improvement_start",
            "original_query": problem_query,
            "timestamp": time.time(),
        }

        try:
            improvement_prompt = get_query_improvement_prompt(problem_query)
            improved_response = self.llm_client.call_llm(improvement_prompt)
            solution = self.response_processor.parse_llm_response(
                improved_response, self.llm_client
            )
            try:
                # Handle object format with "tasks" array
                if isinstance(solution, dict) and "tasks" in solution:
                    tasks = solution["tasks"]

                    # Keep tasks as simple strings without numbering
                    simple_tasks = []
                    for task in tasks:
                        if isinstance(task, str):
                            simple_tasks.append(task)
                        else:
                            # Handle dict format
                            simple_tasks.append(str(task))

                    # Create improved query without numbering for cleaner prompts
                    improved_query = "\n".join(simple_tasks)

                    yield {
                        "type": "query_improved",
                        "original_query": problem_query,
                        "improved_query": improved_query,
                        "tasks": simple_tasks,  # Pass the simple task list without numbering
                        "timestamp": time.time(),
                    }
                else:
                    logger.error(
                        f"Invalid response format - expected object with 'tasks' array, got: {type(solution)}"
                    )
                    raise ValueError("Invalid response format from LLM")

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Raw response: {improved_response}")
                logger.error(f"Cleaned response: {solution}")

                # Use original query as fallback

                logger.warning("Using original query due to JSON parsing failure")
                raise ValueError(f"Invalid JSON response from LLM: {e}")

        except Exception as e:
            logger.warning(f"Failed to improve query: {e}")

            yield {
                "type": "query_improvement_failed",
                "original_query": problem_query,
                "error": str(e),
                "fallback_to_original": True,
                "timestamp": time.time(),
            }

            return
