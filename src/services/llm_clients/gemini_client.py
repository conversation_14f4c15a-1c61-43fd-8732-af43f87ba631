import os
from loguru import logger
from .llm_client_base import LLMClientBase
from google import genai
from ...config import config
import sys


class GeminiClient(LLMClientBase):
    def __init__(self):
        self.project_id = config.llm.gcp.project_id
        self.location = config.llm.gcp.location
        self.model_name = config.llm.gemini_model

        # Set up authentication using service account
        if getattr(sys, "frozen", False):
            # Running as PyInstaller executable
            base_dir = sys._MEIPASS
            service_account_path = os.path.join(
                base_dir, "certs", "hyra-720a2-07066fb5695a.json"
            )
        else:
            # Running from source
            base_dir = os.path.dirname(os.path.abspath(__file__))
            service_account_path = os.path.normpath(
                os.path.join(base_dir, "../../../certs/hyra-720a2-07066fb5695a.json")
            )
        if os.path.exists(service_account_path):
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_path
            logger.debug(f"Using service account file: {service_account_path}")
        else:
            logger.warning(f"Service account file not found at: {service_account_path}")
            logger.warning("Gemini client may fail without proper authentication")

        # Configure the Gemini client with Vertex AI
        try:
            self.client = genai.Client(
                vertexai=True, project=self.project_id, location=self.location
            )
            logger.debug("Gemini client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise

    def call_llm(self, prompt: str) -> str:
        try:
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=[prompt],
                config={
                    "response_mime_type": "application/json",
                },
            )

            if response.text:
                return response.text

            raise Exception("Unexpected response format from Gemini API")

        except Exception as e:
            logger.error(f"Gemini LLM call failed: {e}")
            raise
