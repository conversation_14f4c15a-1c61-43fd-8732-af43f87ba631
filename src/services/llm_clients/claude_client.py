import json
import boto3
from loguru import logger
from .llm_client_base import LLMClientBase
from ...config import config


class ClaudeClient(LLMClientBase):
    def __init__(self, model_id=None, region=None):
        self.model_id = model_id or config.llm.aws.model_id
        self.region = region or config.llm.aws.region
        self._initialize_bedrock_client()

    def _initialize_bedrock_client(self):
        try:
            self.bedrock_client = boto3.client(
                service_name="bedrock-runtime",
                region_name=self.region,
                aws_access_key_id=config.llm.aws.access_key_id,
                aws_secret_access_key=config.llm.aws.secret_access_key,
            )
            logger.info("🤖 Bedrock client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Bedrock client: {e}")
            raise

    def call_llm(self, prompt: str) -> str:
        try:
            body = json.dumps(
                {
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 64000,
                    "messages": [{"role": "user", "content": prompt}],
                }
            )
            response = self.bedrock_client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json",
            )
            response_body = json.loads(response.get("body").read())
            return response_body["content"][0]["text"]
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise
