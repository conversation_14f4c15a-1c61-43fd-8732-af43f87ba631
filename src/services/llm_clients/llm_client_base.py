import json
from loguru import logger

class LLMClientBase:
    @staticmethod
    def clean_json_response(response: str) -> str:
        if not response:
            return response

        response = response.strip()

        # Remove markdown code blocks
        if response.startswith("```json"):
            response = response[7:]
        elif response.startswith("```"):
            response = response[3:]
        if response.endswith("```"):
            response = response[:-3]

        response = response.strip()

        # Try to extract JSON from response if it contains non-JSON text
        # Look for the first { and last } to extract JSON object
        json_start = response.find("{")
        if json_start != -1:
            # Find the matching closing brace
            brace_count = 0
            json_end = -1
            for i in range(json_start, len(response)):
                if response[i] == "{":
                    brace_count += 1
                elif response[i] == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break

            if json_end != -1:
                potential_json = response[json_start:json_end]

                try:
                    final_response = json.loads(potential_json)
                    return final_response
                except json.JSONDecodeError:
                    return potential_json

        final_response = json.loads(response)
        return final_response
