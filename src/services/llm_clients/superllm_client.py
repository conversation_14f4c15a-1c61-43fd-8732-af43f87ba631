import requests
from loguru import logger
from .llm_client_base import LLMClientBase
from ...config import config
from ..auth.token_manager import get_token_manager


class SuperLLMClient(LLMClientBase):
    """
    SuperLLM client that communicates with the SuperLLM API server.
    
    This client requires a Firebase authentication token that users obtain
    from the SuperLLM web interface. The token is used to authenticate
    API requests to the SuperLLM server.
    """
    
    def __init__(self):
        """Initialize the SuperLLM client with configuration."""
        # Load configuration
        if hasattr(config.llm, 'superllm') and config.llm.superllm:
            self.api_endpoint = config.llm.superllm.api_endpoint
            self.default_model = config.llm.superllm.default_model
            self.default_provider = config.llm.superllm.default_provider
            config_token = config.llm.superllm.firebase_token
        else:
            # Use defaults if no configuration
            self.api_endpoint = "http://localhost:8000"
            self.default_model = "gpt-3.5-turbo"
            self.default_provider = "openai"
            config_token = ""

        if not self.api_endpoint:
            raise ValueError(
                "SuperLLM API endpoint must be configured in JSON config for SuperLLMClient."
            )

        # Try to get token from secure storage first, then fall back to config
        token_manager = get_token_manager()
        self.firebase_token = token_manager.get_token('superllm')

        if not self.firebase_token and config_token:
            # Use token from config and store it securely
            self.firebase_token = config_token
            token_manager.store_token('superllm', config_token, {
                'api_endpoint': self.api_endpoint,
                'source': 'config_migration'
            })
            logger.info("Migrated Firebase token from config to secure storage")

        if not self.firebase_token:
            logger.warning(
                "SuperLLM Firebase token not found. "
                "Please run 'sutra auth login' to authenticate with SuperLLM."
            )
            raise ValueError(
                "SuperLLM Firebase token not found. "
                "Please run 'sutra auth login' to authenticate with SuperLLM."
            )

        logger.info("SuperLLM client initialized successfully")
    
    def _get_headers(self) -> dict:
        """Get headers for SuperLLM API requests."""
        return {
            "Authorization": f"Bearer {self.firebase_token}",
            "Content-Type": "application/json",
        }

    def _refresh_token(self) -> bool:
        """
        Attempt to refresh the Firebase token from storage.

        Returns:
            True if token was refreshed, False otherwise
        """
        token_manager = get_token_manager()
        new_token = token_manager.get_token('superllm')

        if new_token and new_token != self.firebase_token:
            self.firebase_token = new_token
            logger.info("Firebase token refreshed from storage")
            return True

        return False
    
    def call_llm(self, prompt: str) -> str:
        """
        Call the SuperLLM API to generate a response.
        
        Args:
            prompt: The input prompt for the LLM
            
        Returns:
            The generated response text
            
        Raises:
            Exception: If the API call fails
        """
        headers = self._get_headers()
        
        # Prepare the request payload in SuperLLM API format
        payload = {
            "messages": [{"role": "user", "content": prompt}],
            "model": self.default_model,
            "provider": self.default_provider,
        }
        
        # Construct the full API URL
        api_url = f"{self.api_endpoint}/api/v1/chat/completions"
        
        try:
            logger.debug(f"Making SuperLLM API request to: {api_url}")
            response = requests.post(api_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract the content from the SuperLLM response format
            if "content" in data:
                return data["content"]
            else:
                logger.error(f"Unexpected SuperLLM response format: {data}")
                raise Exception("Unexpected response format from SuperLLM API")
                
        except requests.exceptions.Timeout:
            logger.error("SuperLLM API request timed out")
            raise Exception("SuperLLM API request timed out")
        except requests.exceptions.ConnectionError:
            logger.error("Failed to connect to SuperLLM API")
            raise Exception("Failed to connect to SuperLLM API")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                logger.error("SuperLLM authentication failed - token may be expired")

                # Try to refresh token once
                if self._refresh_token():
                    logger.info("Retrying with refreshed token")
                    try:
                        # Retry the request with new token
                        headers = self._get_headers()
                        response = requests.post(api_url, headers=headers, json=payload, timeout=60)
                        response.raise_for_status()
                        data = response.json()

                        if "content" in data:
                            return data["content"]
                        else:
                            logger.error(f"Unexpected SuperLLM response format: {data}")
                            raise Exception("Unexpected response format from SuperLLM API")
                    except Exception:
                        pass  # Fall through to original error

                raise Exception(
                    "SuperLLM authentication failed. "
                    "Please run 'sutra auth login' to re-authenticate with SuperLLM."
                )
            elif e.response.status_code == 429:
                logger.error("SuperLLM rate limit exceeded")
                raise Exception("SuperLLM rate limit exceeded. Please try again later.")
            else:
                logger.error(f"SuperLLM API HTTP error: {e.response.status_code} - {e.response.text}")
                raise Exception(f"SuperLLM API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"SuperLLM API call failed: {e}")
            raise
