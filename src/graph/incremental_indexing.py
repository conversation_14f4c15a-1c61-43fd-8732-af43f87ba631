"""Incremental indexing for efficient database updates when code changes."""

import asyncio
import hashlib
import os
from typing import Dict, List, Optional, Set, Any
from loguru import logger

from ..graph.sqlite_client import SQLiteConnection, GraphOperations
from ..graph.converter import TreeSitterToSQLiteConverter
from ..processors.data_processor import GraphDataProcessor
from ..models.schema import ParsedCodebase, GraphData
from ..utils.helpers import load_json_file
from ..parser.analyzer.analyzer import Analyzer
from ..config.settings import config


class IncrementalIndexing:
    """Handles incremental indexing of code changes to update the database efficiently."""

    def __init__(self, sqlite_connection: Optional[SQLiteConnection] = None):
        """Initialize with optional connection."""
        self.connection = sqlite_connection or SQLiteConnection()
        self.converter = TreeSitterToSQLiteConverter(self.connection)
        self.processor = GraphDataProcessor(self.connection)
        self.graphOperations = GraphOperations(self.connection)
        logger.debug("🔄 IncrementalIndexing initialized")

    def reindex_database(
        self, project_name: str, create_indexes: bool = True
    ) -> Dict[str, Any]:
        """Update database with changes from new parser output.

        Args:
            project_name: Name of the project/codebase
            create_indexes: Whether to create indexes after update

        Returns:
            Dictionary with update statistics
        """
        logger.debug(f"🔄 Starting incremental update for project: {project_name}")

        try:
            # Get project ID
            project_id = self._get_project_id(project_name)
            if not project_id:
                logger.error(f"Project '{project_name}' not found in database")
                return {
                    "status": "failed",
                    "error": f"Project '{project_name}' not found",
                }

            start_node_id = self._get_last_node_id(project_id)

            analyzer = Analyzer(repo_id=project_id, start_node_id=start_node_id)

            cur_dir = os.getcwd()
            # Run the async analyze_and_save method using configured parser results directory
            json_file_path = asyncio.run(
                analyzer.analyze_and_save(
                    directory_path=cur_dir,
                    results_folder=config.storage.parser_results_dir,
                )
            )

            # Load and parse JSON data
            logger.debug(f"📦 Loading JSON data from {json_file_path}")
            json_data = load_json_file(json_file_path)

            parsed_data = self.converter._parse_json_data(
                json_data, project_name, json_file_path
            )

            # Compare file hashes and identify changes
            changes = self._identify_changes(parsed_data, project_id)

            # Process changes
            stats = self._process_changes(
                changes, parsed_data, project_id, project_name
            )

            logger.debug("🔢 Creating database indexes...")
            self.connection.create_indexes()

            logger.debug(f"✅ Incremental update completed successfully!")
            return {
                "status": "success",
                "input_file": json_file_path,
                "files_changed": len(changes["changed_files"]),
                "files_added": len(changes["new_files"]),
                "files_deleted": len(changes["deleted_files"]),
                "nodes_deleted": stats["nodes_deleted"],
                "relationships_deleted": stats["relationships_deleted"],
                "nodes_added": stats["nodes_added"],
                "relationships_added": stats["relationships_added"],
            }

        except Exception as e:
            logger.error(f"Incremental update failed: {e}")
            return {"status": "failed", "error": str(e), "input_file": json_file_path}

    def _get_project_id(self, project_name: str) -> Optional[int]:
        """Get project ID from project name."""
        try:
            result = self.connection.execute_query(
                "SELECT id FROM projects WHERE name = ?", (project_name,)
            )
            return result[0]["id"] if result else None
        except Exception as e:
            logger.error(f"Error getting project ID: {e}")
            return None

    def _get_last_node_id(self, project_id: int) -> int:
        """Get the highest node_id for the project to prevent overlap."""
        try:
            result = self.connection.execute_query(
                "SELECT MAX(node_id) as max_id FROM nodes WHERE project_id = ?",
                (project_id,),
            )
            return (
                result[0]["max_id"] if result and result[0]["max_id"] is not None else 0
            )
        except Exception as e:
            logger.error(f"Error getting last node ID: {e}")
            return 0

    def _identify_changes(
        self, parsed_data: ParsedCodebase, project_id: int
    ) -> Dict[str, Set[str]]:
        """Identify changed, new, and deleted files by comparing file hashes."""
        # Get all file hashes from the database for this project
        db_file_hashes = self._get_db_file_hashes(project_id)

        # Extract file hashes from parsed data
        parser_file_hashes = self._get_parser_file_hashes(parsed_data)

        # Identify changed, new, and deleted files
        changed_files = set()
        new_files = set()

        for file_path, parser_hash in parser_file_hashes.items():
            if file_path in db_file_hashes:
                if db_file_hashes[file_path] != parser_hash:
                    # File exists but hash changed (modified)
                    changed_files.add(file_path)
            else:
                # File doesn't exist in database (new)
                new_files.add(file_path)

        # Identify deleted files (in DB but not in parser output)
        deleted_files = set(db_file_hashes.keys()) - set(parser_file_hashes.keys())

        logger.debug(
            f"📊 Changes identified: {len(changed_files)} changed, {len(new_files)} new, {len(deleted_files)} deleted"
        )
        return {
            "changed_files": changed_files,
            "new_files": new_files,
            "deleted_files": deleted_files,
        }

    def _get_db_file_hashes(self, project_id: int) -> Dict[str, str]:
        """Get all file hashes from the database for a project."""
        file_hashes = {}
        try:
            results = self.connection.execute_query(
                """SELECT file_path, content_hash
                   FROM file_hashes
                   WHERE project_id = ?""",
                (project_id,),
            )

            for row in results:
                file_hashes[row["file_path"]] = row["content_hash"]

            logger.debug(f"📊 Retrieved {len(file_hashes)} file hashes from database")
            return file_hashes
        except Exception as e:
            logger.error(f"Error getting file hashes from database: {e}")
            return {}

    def _get_parser_file_hashes(self, parsed_data: ParsedCodebase) -> Dict[str, str]:
        """Extract file hashes from parsed data."""
        file_hashes = {}

        for node in parsed_data.nodes:
            if node.type == "file" and node.path:
                # Use content_hash if available, otherwise compute it
                content_hash = (
                    node.content_hash
                    or hashlib.sha256(node.content.encode("utf-8")).hexdigest()
                    if node.content
                    else ""
                )

                file_hashes[node.path] = content_hash

        logger.debug(f"📊 Extracted {len(file_hashes)} file hashes from parser output")
        return file_hashes

    def _process_changes(
        self,
        changes: Dict[str, Set[str]],
        parsed_data: ParsedCodebase,
        project_id: int,
        project_name: str,
    ) -> Dict[str, int]:
        """Process identified changes by deleting and adding nodes and relationships."""
        # Initialize counters
        nodes_deleted = 0
        relationships_deleted = 0
        nodes_added = 0
        relationships_added = 0

        # Process changed and deleted files (delete their nodes and relationships)
        files_to_delete = changes["changed_files"].union(changes["deleted_files"])
        for file_path in files_to_delete:
            deleted = self._delete_file_nodes_and_relationships(file_path, project_id)
            nodes_deleted += deleted["nodes"]
            relationships_deleted += deleted["relationships"]

        # Process changed and new files (add their nodes and relationships)
        files_to_add = changes["changed_files"].union(changes["new_files"])
        if files_to_add:
            # Filter nodes and relationships for files to add
            filtered_nodes = [n for n in parsed_data.nodes if n.path in files_to_add]

            # For relationships, we need to check if either the source or target node is in a changed file
            # This requires mapping node IDs to file paths
            node_id_to_file = {n.id: n.path for n in parsed_data.nodes if n.path}

            # Get the node IDs that will be inserted
            filtered_node_ids = {n.id for n in filtered_nodes}

            filtered_edges = []
            for edge in parsed_data.edges:
                # Only include edge if BOTH source and target nodes are being inserted
                # This prevents foreign key constraint violations
                if edge.from_id in filtered_node_ids and (
                    edge.to_id is None or edge.to_id in filtered_node_ids
                ):
                    filtered_edges.append(edge)

            # Create a filtered parsed data object
            filtered_data = ParsedCodebase(
                nodes=filtered_nodes, edges=filtered_edges, project=parsed_data.project
            )

            # Process the filtered data
            graph_data = self.processor.process_codebase(
                filtered_data, project_id, project_name
            )

            # Insert the processed data
            self._insert_graph_data(graph_data, project_id)

            nodes_added = len(graph_data.nodes)
            relationships_added = len(graph_data.relationships)

        logger.debug(
            f"📊 Processed changes: {nodes_deleted} nodes deleted, {relationships_deleted} relationships deleted"
        )
        logger.debug(
            f"📊 Processed changes: {nodes_added} nodes added, {relationships_added} relationships added"
        )

        return {
            "nodes_deleted": nodes_deleted,
            "relationships_deleted": relationships_deleted,
            "nodes_added": nodes_added,
            "relationships_added": relationships_added,
        }

    def _delete_file_nodes_and_relationships(
        self, file_path: str, project_id: int
    ) -> Dict[str, int]:
        """Delete all nodes and relationships for a specific file."""
        try:
            # Get file hash ID for the file path
            file_hash_result = self.connection.execute_query(
                "SELECT id FROM file_hashes WHERE project_id = ? AND file_path = ?",
                (project_id, file_path),
            )

            if not file_hash_result:
                logger.warning(f"File hash not found for {file_path}")
                return {"nodes": 0, "relationships": 0}

            file_hash_id = file_hash_result[0]["id"]

            # Get all node IDs for this file hash
            node_id_result = self.connection.execute_query(
                "SELECT node_id FROM nodes WHERE project_id = ? AND file_hash_id = ?",
                (project_id, file_hash_id),
            )

            if not node_id_result:
                logger.warning(f"No nodes found for file {file_path}")
                return {"nodes": 0, "relationships": 0}

            node_ids = [row["node_id"] for row in node_id_result]

            # Delete relationships involving these nodes
            # First, count them
            rel_count_result = self.connection.execute_query(
                """SELECT COUNT(*) as count FROM relationships
                   WHERE project_id = ? AND (from_node_id IN ({}) OR to_node_id IN ({}))""".format(
                    ",".join(["?" for _ in node_ids]), ",".join(["?" for _ in node_ids])
                ),
                (project_id, *node_ids, *node_ids),
            )

            rel_count = rel_count_result[0]["count"] if rel_count_result else 0

            # Then delete them
            self.connection.execute_query(
                """DELETE FROM relationships
                   WHERE project_id = ? AND (from_node_id IN ({}) OR to_node_id IN ({}))""".format(
                    ",".join(["?" for _ in node_ids]), ",".join(["?" for _ in node_ids])
                ),
                (project_id, *node_ids, *node_ids),
            )

            # Delete nodes
            self.connection.execute_query(
                "DELETE FROM nodes WHERE project_id = ? AND file_hash_id = ?",
                (project_id, file_hash_id),
            )

            # Delete file hash
            self.connection.execute_query(
                "DELETE FROM file_hashes WHERE id = ?", (file_hash_id,)
            )

            # Delete embeddings for these nodes
            self._delete_node_embeddings(node_ids, project_id)

            logger.debug(
                f"🗑️ Deleted {len(node_ids)} nodes and {rel_count} relationships for file {file_path}"
            )
            return {"nodes": len(node_ids), "relationships": rel_count}

        except Exception as e:
            logger.error(f"Error deleting nodes for file {file_path}: {e}")
            return {"nodes": 0, "relationships": 0}

    def _delete_node_embeddings(self, node_ids: List[int], project_id: int) -> None:
        """Delete embeddings for specified nodes."""
        try:
            # Check if vector_embeddings table exists
            table_check = self.connection.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='vector_embeddings'"
            )

            if not table_check:
                logger.debug(
                    "vector_embeddings table does not exist, skipping embedding deletion"
                )
                return

            # Delete embeddings for these nodes
            placeholders = ",".join(["?" for _ in node_ids])
            self.connection.execute_query(
                f"""DELETE FROM vector_embeddings
                   WHERE node_id IN ({placeholders}) AND project_id = ?""",
                (*node_ids, project_id),
            )

            logger.debug(f"Deleted embeddings for {len(node_ids)} nodes")

        except Exception as e:
            logger.error(f"Error deleting node embeddings: {e}")

    def _insert_graph_data(self, graph_data: GraphData, project_id: int) -> None:
        """Insert graph data into the database."""
        try:

            # Insert nodes
            if graph_data.nodes:
                self.graphOperations.insert_nodes(graph_data.nodes, project_id)

            # Insert relationships
            if graph_data.relationships:
                self.graphOperations.insert_relationships(
                    graph_data.relationships, project_id
                )

        except Exception as e:
            logger.error(f"Error inserting graph data: {e}")
            raise
