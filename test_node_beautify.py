#!/usr/bin/env python3
"""
Test script to fetch a node from the database and beautify it with line numbers.
Usage: python test_node_beautify.py <file_path> [node_id]
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.sqlite_client import SQLiteConnection
from src.services.agent.action_executor.utils.database_utils import get_node_details
from src.services.agent.action_executor.utils.formatting_utils import beautify_node_result
from src.services.agent.action_executor.utils.code_processing_utils import add_line_numbers_to_code
from loguru import logger


def find_nodes_by_file_path(file_path: str, db_connection=None):
    """
    Find all nodes for a given file path.
    
    Args:
        file_path: The file path to search for
        db_connection: Optional database connection
        
    Returns:
        List of node dictionaries
    """
    try:
        db_connection = db_connection or SQLiteConnection()
        cursor = db_connection.connection.cursor()
        
        # Query to find nodes by file path
        query = """
            SELECT 
                n.node_id,
                n.node_type,
                n.name,
                n.lines,
                fh.file_path,
            FROM nodes n
            LEFT JOIN file_hashes fh ON n.file_hash_id = fh.id
            WHERE fh.file_path = ?
        """
        
        cursor.execute(query, (file_path,))
        rows = cursor.fetchall()
        
        nodes = []
        for row in rows:
            nodes.append({
                "node_id": row[0],
                "node_type": row[1],
                "name": row[2],
                "lines": row[3],
                "file_path": row[4],
                "project_name": row[5]
            })
        
        return nodes
        
    except Exception as e:
        logger.error(f"Error finding nodes by file path: {e}")
        return []


def test_node_beautify(file_path: str, node_id: int = None):
    """
    Test the node beautification with line numbers.
    
    Args:
        file_path: Path to the file to search for nodes
        node_id: Optional specific node ID to test
    """
    try:
        print(f"🔍 Searching for nodes in file: {file_path}")
        print("=" * 60)
        
        # Initialize database connection
        db_connection = SQLiteConnection()
        
        if node_id:
            # Test specific node
            print(f"📋 Fetching specific node ID: {node_id}")
            node_details = get_node_details(node_id, db_connection=db_connection)
            
            if not node_details:
                print(f"❌ Node {node_id} not found!")
                return
                
            nodes_to_test = [node_details]
        else:
            # Find all nodes for the file
            nodes = find_nodes_by_file_path(file_path, db_connection)
            
            if not nodes:
                print(f"❌ No nodes found for file: {file_path}")
                return
                
            print(f"📋 Found {len(nodes)} nodes in file:")
            for i, node in enumerate(nodes, 1):
                print(f"  {i}. Node ID: {node['node_id']}, Type: {node['node_type']}, Name: {node['name']}")
            
            print("\n" + "=" * 60)
            
            # Get detailed information for each node
            nodes_to_test = []
            for node in nodes:
                node_details = get_node_details(node['node_id'], db_connection=db_connection)
                if node_details:
                    nodes_to_test.append(node_details)
        
        # Test beautification for each node
        for i, node_details in enumerate(nodes_to_test, 1):
            print(f"\n🎨 Testing beautification for Node {i}/{len(nodes_to_test)}")
            print(f"Node ID: {node_details['node_id']}")
            print("-" * 40)
            
            # Test 1: Beautify with code (includes line numbers)
            print("📝 Test 1: Beautify with code (includes line numbers)")
            beautified_with_code = beautify_node_result(
                node_details, 
                idx=i, 
                include_code=True, 
                total_nodes=len(nodes_to_test)
            )
            print(beautified_with_code)
            
            print("\n" + "-" * 40)
            
            # Test 2: Beautify without code (metadata only)
            print("📋 Test 2: Beautify without code (metadata only)")
            beautified_without_code = beautify_node_result(
                node_details, 
                idx=i, 
                include_code=False, 
                total_nodes=len(nodes_to_test)
            )
            print(beautified_without_code)
            
            print("\n" + "-" * 40)
            
            # Test 3: Manual line number addition
            code_snippet = node_details.get('code_snippet', '')
            if code_snippet:
                print("🔢 Test 3: Manual line number addition")
                
                # Parse line information
                lines = node_details.get('lines')
                start_line = None
                if lines:
                    try:
                        import json
                        lines_data = json.loads(lines) if isinstance(lines, str) else lines
                        if isinstance(lines_data, list) and len(lines_data) >= 2:
                            start_line = lines_data[0]
                    except:
                        pass
                
                numbered_code = add_line_numbers_to_code(code_snippet, start_line)
                print("Raw code with line numbers:")
                print(numbered_code)
            else:
                print("⚠️  No code snippet available for this node")
            
            if i < len(nodes_to_test):
                print("\n" + "=" * 60)
        
        print(f"\n✅ Successfully tested beautification for {len(nodes_to_test)} nodes!")
        
    except Exception as e:
        logger.error(f"Error in test_node_beautify: {e}")
        print(f"❌ Error: {e}")


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        print("🧪 Node Beautification Test Script")
        print("=" * 50)
        print("This script fetches nodes from the database and tests the beautify function with line numbers.")
        print()
        print("Usage: python test_node_beautify.py <file_path> [node_id]")
        print()
        print("Arguments:")
        print("  file_path  - Path to the file to search for nodes")
        print("  node_id    - Optional specific node ID to test (integer)")
        print()
        print("Examples:")
        print("  # Test all nodes in a file:")
        print("  python test_node_beautify.py /home/<USER>/Hyrr/datalayer/src/cache/index.js")
        print()
        print("  # Test a specific node:")
        print("  python test_node_beautify.py /home/<USER>/Hyrr/datalayer/src/cache/index.js 24")
        print()
        print("Features tested:")
        print("  ✓ Database node fetching")
        print("  ✓ Beautify with code (includes automatic line numbers)")
        print("  ✓ Beautify without code (metadata only)")
        print("  ✓ Manual line number addition using add_line_numbers_to_code()")
        sys.exit(1)
    
    file_path = sys.argv[1]
    node_id = None
    
    if len(sys.argv) > 2:
        try:
            node_id = int(sys.argv[2])
        except ValueError:
            print(f"❌ Invalid node_id: {sys.argv[2]}. Must be an integer.")
            sys.exit(1)
    
    # Convert relative path to absolute if needed
    if not file_path.startswith('/'):
        # Assume it's relative to the current working directory
        file_path = os.path.abspath(file_path)
    
    print(f"🚀 Starting node beautification test")
    print(f"File path: {file_path}")
    if node_id:
        print(f"Node ID: {node_id}")
    print()
    
    test_node_beautify(file_path, node_id)


if __name__ == "__main__":
    main()
