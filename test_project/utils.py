"""Utility classes for the test project - MODIFIED."""

class Calculator:
    """A simple calculator class with more operations."""
    
    def __init__(self):
        self.history = []
        self.precision = 2  # New attribute
    
    def add(self, a, b):
        """Add two numbers."""
        result = a + b
        self.history.append(f"add({a}, {b}) = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers."""
        result = a * b
        self.history.append(f"multiply({a}, {b}) = {result}")
        return result
    
    def subtract(self, a, b):
        """Subtract two numbers - NEW METHOD."""
        result = a - b
        self.history.append(f"subtract({a}, {b}) = {result}")
        return result
    
    def divide(self, a, b):
        """Divide two numbers - NEW METHOD."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = round(a / b, self.precision)
        self.history.append(f"divide({a}, {b}) = {result}")
        return result
    
    def get_history(self):
        """Get calculation history - NEW METHOD."""
        return self.history.copy()

class Logger:
    """A simple logging class with enhanced features."""
    
    def __init__(self, prefix="LOG"):
        self.logs = []
        self.prefix = prefix  # New attribute
    
    def log(self, message):
        """Log a message."""
        formatted_message = f"{self.prefix}: {message}"
        self.logs.append(formatted_message)
        print(formatted_message)
    
    def get_logs(self):
        """Get all logs - NEW METHOD."""
        return self.logs.copy()
    
    def clear_logs(self):
        """Clear all logs - NEW METHOD."""
        self.logs.clear()

class MathHelper:
    """New class for mathematical utilities."""
    
    @staticmethod
    def factorial(n):
        """Calculate factorial of n."""
        if n < 0:
            raise ValueError("Factorial not defined for negative numbers")
        if n == 0 or n == 1:
            return 1
        return n * MathHelper.factorial(n - 1)
    
    @staticmethod
    def fibonacci(n):
        """Calculate nth Fibonacci number."""
        if n < 0:
            raise ValueError("Fibonacci not defined for negative numbers")
        if n == 0:
            return 0
        if n == 1:
            return 1
        return MathHelper.fibonacci(n - 1) + MathHelper.fibonacci(n - 2)
