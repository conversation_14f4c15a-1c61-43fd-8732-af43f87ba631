"""Main module for the test project - ENHANCED."""

from utils import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MathHelper

def main():
    """Enhanced main function with more operations."""
    calc = Calculator()
    logger = Logger("MAIN")
    
    # Basic operations
    result = calc.add(5, 3)
    logger.log(f"Addition result: {result}")
    
    result = calc.multiply(4, 7)
    logger.log(f"Multiplication result: {result}")
    
    # New operations
    result = calc.subtract(10, 3)
    logger.log(f"Subtraction result: {result}")
    
    result = calc.divide(15, 3)
    logger.log(f"Division result: {result}")
    
    # Use MathHelper
    factorial_result = MathHelper.factorial(5)
    logger.log(f"Factorial of 5: {factorial_result}")
    
    fibonacci_result = MathHelper.fibonacci(8)
    logger.log(f"8th Fibonacci number: {fibonacci_result}")
    
    # Show history
    history = calc.get_history()
    logger.log(f"Calculation history has {len(history)} entries")
    
    return result

def run_tests():
    """New function to run tests."""
    logger = Logger("TEST")
    logger.log("Running tests...")
    
    # Test Calculator
    calc = Calculator()
    assert calc.add(2, 3) == 5
    assert calc.multiply(4, 5) == 20
    logger.log("Calculator tests passed")
    
    # Test MathHelper
    assert MathHelper.factorial(4) == 24
    assert MathHelper.fibonacci(5) == 5
    logger.log("MathHelper tests passed")
    
    logger.log("All tests completed successfully")

if __name__ == "__main__":
    main()
    run_tests()
