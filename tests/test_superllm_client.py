"""
Test cases for SuperLLM client integration.
"""

import pytest
import json
import tempfile
import os
from unittest.mock import patch, Mo<PERSON>, MagicMock
import requests

from sutraknowledge.src.services.llm_clients.superllm_client import SuperLLMClient
from sutraknowledge.src.services.llm_clients.llm_factory import llm_client_factory


class TestSuperLLMClient:
    """Test cases for SuperLLM client."""
    
    def setup_method(self):
        """Set up test configuration."""
        self.test_config = {
            "database": {
                "knowledge_graph_db": "test.db",
                "embeddings_db": "test_embeddings.db",
                "connection_timeout": 30,
                "max_retry_attempts": 3,
                "batch_size": 1000,
                "enable_indexing": True,
                "create_tables": True,
                "enable_wal_mode": True
            },
            "storage": {
                "data_dir": "test_data",
                "sessions_dir": "test_data/sessions",
                "file_changes_dir": "test_data/file_changes",
                "file_edits_dir": "test_data/edits",
                "parser_results_dir": "test_data/parser_results",
                "models_dir": "test_models"
            },
            "embedding": {
                "model_name": "all-MiniLM-L6-v2",
                "model_path": "test_models/all-MiniLM-L6-v2",
                "max_tokens": 256,
                "overlap_tokens": 25,
                "similarity_threshold": 0.2,
                "enable_optimization": False
            },
            "parser": {
                "config_file": "test_parsers.json",
                "build_directory": "test_build"
            },
            "logging": {
                "level": "DEBUG",
                "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
                "log_file": "test.log"
            },
            "llm": {
                "provider": "superllm",
                "llama_model_id": "meta/llama-4-maverick-17b-128e-instruct-maas",
                "claude_model": "claude-3-7-sonnet@20250219",
                "gemini_model": "gemini-2.5-flash",
                "aws": {
                    "model_id": "test-model",
                    "access_key_id": "test-key",
                    "secret_access_key": "test-secret",
                    "region": "us-east-1"
                },
                "gcp": {
                    "api_key": "test-gcp-key",
                    "project_id": "test-project",
                    "location": "us-east5",
                    "llm_endpoint": "https://test-endpoint.com"
                },
                "superllm": {
                    "api_endpoint": "http://localhost:8000",
                    "firebase_token": "test-firebase-token",
                    "default_model": "gpt-3.5-turbo",
                    "default_provider": "openai"
                }
            }
        }
    
    def test_superllm_client_initialization_success(self):
        """Test successful SuperLLM client initialization."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                # Reset the config instance to force reload
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                client = SuperLLMClient()
                assert client.api_endpoint == "http://localhost:8000"
                assert client.firebase_token == "test-firebase-token"
                assert client.default_model == "gpt-3.5-turbo"
                assert client.default_provider == "openai"
        finally:
            os.unlink(config_path)
    
    def test_superllm_client_missing_endpoint(self):
        """Test SuperLLM client initialization with missing endpoint."""
        config_without_endpoint = self.test_config.copy()
        config_without_endpoint["llm"]["superllm"]["api_endpoint"] = ""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_without_endpoint, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                with pytest.raises(ValueError, match="SuperLLM API endpoint must be configured"):
                    SuperLLMClient()
        finally:
            os.unlink(config_path)
    
    def test_superllm_client_missing_token(self):
        """Test SuperLLM client initialization with missing Firebase token."""
        config_without_token = self.test_config.copy()
        config_without_token["llm"]["superllm"]["firebase_token"] = ""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_without_token, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                with pytest.raises(ValueError, match="SuperLLM Firebase token must be configured"):
                    SuperLLMClient()
        finally:
            os.unlink(config_path)
    
    @patch('requests.post')
    def test_call_llm_success(self, mock_post):
        """Test successful LLM call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "test-id",
            "model": "gpt-3.5-turbo",
            "content": "Hello! How can I help you today?",
            "finish_reason": "stop",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 8,
                "total_tokens": 18
            },
            "provider": "openai"
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                client = SuperLLMClient()
                result = client.call_llm("Hello, how are you?")
                
                assert result == "Hello! How can I help you today?"
                
                # Verify the request was made correctly
                mock_post.assert_called_once()
                args, kwargs = mock_post.call_args
                
                assert args[0] == "http://localhost:8000/api/v1/chat/completions"
                assert kwargs['headers']['Authorization'] == "Bearer test-firebase-token"
                assert kwargs['headers']['Content-Type'] == "application/json"
                assert kwargs['json']['messages'] == [{"role": "user", "content": "Hello, how are you?"}]
                assert kwargs['json']['model'] == "gpt-3.5-turbo"
                assert kwargs['json']['provider'] == "openai"
                assert kwargs['timeout'] == 60
        finally:
            os.unlink(config_path)
    
    @patch('requests.post')
    def test_call_llm_authentication_error(self, mock_post):
        """Test LLM call with authentication error."""
        # Mock 401 response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        mock_post.return_value = mock_response
        mock_post.return_value.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_response)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                client = SuperLLMClient()
                
                with pytest.raises(Exception, match="SuperLLM authentication failed"):
                    client.call_llm("Hello, how are you?")
        finally:
            os.unlink(config_path)
    
    def test_factory_creates_superllm_client(self):
        """Test that the factory creates SuperLLM client when configured."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            config_path = f.name
        
        try:
            with patch.dict(os.environ, {'SUTRAKNOWLEDGE_CONFIG': config_path}):
                from sutraknowledge.src.config.settings import _config_instance
                _config_instance = None
                
                client = llm_client_factory()
                assert isinstance(client, SuperLLMClient)
        finally:
            os.unlink(config_path)
